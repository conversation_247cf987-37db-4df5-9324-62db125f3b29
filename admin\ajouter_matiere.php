<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';
verifier_connexion();
verifier_role('admin');

$success = '';
$error = '';
$id_concours = '';
$nom_matiere = '';
$coefficient = 1;

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id_concours = $_POST['id_concours'] ?? '';
    $nom_matiere = htmlspecialchars(trim($_POST['nom_matiere'] ?? ''));
    $coefficient = floatval($_POST['coefficient'] ?? 1);

    if ($id_concours && $nom_matiere) {
        // Vérifier si le concours existe
        $check = $pdo->prepare("SELECT COUNT(*) FROM concours WHERE id = ?");
        $check->execute([$id_concours]);
        if ($check->fetchColumn() == 0) {
            $error = "Concours invalide.";
        } else {
            // Insérer la matière
            $stmt = $pdo->prepare("INSERT INTO matiere (nom, coefficient, id_concours) VALUES (?, ?, ?)");
            if ($stmt->execute([$nom_matiere, $coefficient, $id_concours])) {
                $success = "Matière ajoutée avec succès.";
                // Réinitialiser les champs
                $id_concours = '';
                $nom_matiere = '';
                $coefficient = 1;
            } else {
                $error = "Erreur lors de l'ajout de la matière.";
            }
        }
    } else {
        $error = "Veuillez remplir tous les champs.";
    }
}

// Récupération des concours pour la liste déroulante
$concours = $pdo->query("SELECT id, titre FROM concours ORDER BY titre")->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Ajouter une matière</title>
    <link rel="stylesheet" href="../public/css/style.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f4f6f9;
        }
        form {
            background-color: white;
            padding: 25px;
            border-radius: 6px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: auto;
        }
        h2 {
            text-align: center;
            color: #2c3e50;
        }
        label {
            display: block;
            margin-top: 10px;
            font-weight: bold;
        }
        select, input[type="text"], input[type="number"] {
            width: 100%;
            padding: 8px;
            margin-top: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            margin-top: 15px;
            padding: 10px;
            background-color: #2980b9;
            color: white;
            border: none;
            width: 100%;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #1f5a87;
        }
        .message {
            text-align: center;
            padding: 10px;
            margin-bottom: 15px;
            border-radius: 4px;
        }
        .success { background-color: #dff0d8; color: #3c763d; }
        .error { background-color: #f2dede; color: #a94442; }
        a.back {
            display: block;
            text-align: center;
            margin-top: 20px;
            text-decoration: none;
            color: #7f8c8d;
        }
        a.back:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>

<h2>Ajouter une matière à un concours</h2>

<?php if ($success): ?>
    <div class="message success"><?= htmlspecialchars($success) ?></div>
<?php elseif ($error): ?>
    <div class="message error"><?= htmlspecialchars($error) ?></div>
<?php endif; ?>

<form method="POST">
    <label for="id_concours">Concours :</label>
    <select name="id_concours" required>
        <option value="">-- Sélectionner un concours --</option>
        <?php foreach ($concours as $c): ?>
            <option value="<?= $c['id'] ?>" <?= ($id_concours == $c['id']) ? 'selected' : '' ?>>
                <?= htmlspecialchars($c['titre']) ?>
            </option>
        <?php endforeach; ?>
    </select>

    <label for="nom_matiere">Nom de la matière :</label>
    <input type="text" name="nom_matiere" value="<?= htmlspecialchars($nom_matiere) ?>" placeholder="Ex: Mathématiques" required>

    <label for="coefficient">Coefficient :</label>
    <input type="number" name="coefficient" value="<?= htmlspecialchars($coefficient) ?>" min="0.1" step="0.1" required>

    <button type="submit">Ajouter</button>
</form>

<a class="back" href="concours.php">← Retour à la liste des concours</a>

</body>
</html>
