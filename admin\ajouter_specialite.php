<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';
verifier_connexion();
verifier_role('admin');

// Récupération des correcteurs
$correcteurs = $pdo->query("
    SELECT u.id, u.nom, u.prenom, GROUP_CONCAT(cs.specialite) as specialites
    FROM users u
    LEFT JOIN correcteurs_specialites cs ON u.id = cs.user_id
    WHERE u.role = 'correcteur'
    GROUP BY u.id
    ORDER BY u.nom
")->fetchAll();

// Récupération des matières disponibles
$matieres = $pdo->query("
    SELECT DISTINCT nom 
    FROM matiere 
    ORDER BY nom
")->fetchAll(PDO::FETCH_COLUMN);

// Traitement de l'ajout de spécialité
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id_correcteur = intval($_POST['correcteur']);
    $specialite = trim($_POST['specialite']);

    if (!empty($specialite)) {
        try {
            $stmt = $pdo->prepare("INSERT INTO correcteurs_specialites (user_id, specialite) VALUES (?, ?)");
            $stmt->execute([$id_correcteur, $specialite]);
            $_SESSION['success'] = "Spécialité ajoutée avec succès.";
        } catch (PDOException $e) {
            $_SESSION['error'] = "Erreur lors de l'ajout de la spécialité.";
        }
    } else {
        $_SESSION['error'] = "La spécialité ne peut pas être vide.";
    }
    
    header("Location: ajouter_specialite.php");
    exit;
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Gestion des Spécialités</title>
    <link rel="stylesheet" href="../public/css/style.css">
    <style>
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
        }
        .form-group select, .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            text-align: center;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .correcteur-list {
            margin-top: 30px;
        }
        .correcteur-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .specialites {
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Gestion des Spécialités des Correcteurs</h2>
        <a href="dashboard.php" class="back-link">← Retour</a>

        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <?= $_SESSION['success'] ?>
                <?php unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-error">
                <?= $_SESSION['error'] ?>
                <?php unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>

        <form method="POST" class="form-group">
            <div>
                <label>Correcteur:</label>
                <select name="correcteur" required>
                    <?php foreach ($correcteurs as $c): ?>
                        <option value="<?= $c['id'] ?>">
                            <?= htmlspecialchars($c['prenom'] . ' ' . $c['nom']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div>
                <label>Spécialité:</label>
                <select name="specialite" required>
                    <?php foreach ($matieres as $matiere): ?>
                        <option value="<?= htmlspecialchars($matiere) ?>">
                            <?= htmlspecialchars($matiere) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <button type="submit">Ajouter la spécialité</button>
        </form>

        <div class="correcteur-list">
            <h3>Liste des Correcteurs et leurs Spécialités</h3>
            <?php foreach ($correcteurs as $c): ?>
                <div class="correcteur-item">
                    <strong><?= htmlspecialchars($c['prenom'] . ' ' . $c['nom']) ?></strong>
                    <div class="specialites">
                        Spécialités: <?= $c['specialites'] ? htmlspecialchars($c['specialites']) : 'Aucune spécialité' ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</body>
</html> 