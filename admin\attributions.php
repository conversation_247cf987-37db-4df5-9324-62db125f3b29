<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';
verifier_connexion();
verifier_role('admin');

// Récupération des correcteurs avec leurs spécialités
$correcteurs = $pdo->query("
    SELECT id, nom, prenom, specialite 
    FROM users 
    WHERE role = 'correcteur' 
    ORDER BY specialite, nom
")->fetchAll();

// Récupération des matières
$matieres = $pdo->query("
    SELECT m.*, c.titre as concours_nom 
    FROM matiere m 
    JOIN concours c ON m.id_concours = c.id 
    ORDER BY c.titre, m.nom
")->fetchAll();

// Traitement de l'attribution
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id_correcteur = intval($_POST['correcteur']);
    $id_matiere = intval($_POST['matiere']);
    $nb_copies = intval($_POST['nb_copies']);

    // Vérifier si la matière existe
    $verif_matiere = $pdo->prepare("SELECT id FROM matiere WHERE id = ?");
    $verif_matiere->execute([$id_matiere]);
    if (!$verif_matiere->fetch()) {
        $_SESSION['message'] = "La matière sélectionnée n'existe pas.";
        header('Location: ' . $_SERVER['PHP_SELF']);
        exit();
    }

    // Mettre à jour les copies sans matière
    $update_copies = $pdo->prepare("
        UPDATE copie 
        SET id_matiere = ? 
        WHERE id_matiere IS NULL 
        AND id_concours = (SELECT id_concours FROM matiere WHERE id = ?)
    ");
    $update_copies->bindValue(1, $id_matiere, PDO::PARAM_INT);
    $update_copies->bindValue(2, $id_matiere, PDO::PARAM_INT);
    $update_copies->execute();

    // Vérifier s'il y a des copies disponibles pour cette matière
    $verif_copies = $pdo->prepare("
        SELECT COUNT(*) as total 
        FROM copie cp
        LEFT JOIN attribution a ON cp.id = a.id_copie
        WHERE cp.id_matiere = ? AND a.id IS NULL
    ");
    $verif_copies->execute([$id_matiere]);
    $total_copies = $verif_copies->fetch()['total'];

    if ($total_copies == 0) {
        $_SESSION['message'] = "Aucune copie disponible pour cette matière.";
        header('Location: ' . $_SERVER['PHP_SELF']);
        exit();
    }

    // Récupérer les copies non attribuées pour cette matière
    $copies = $pdo->prepare("
        SELECT id FROM (
            SELECT cp.id 
            FROM copie cp
            LEFT JOIN attribution a ON cp.id = a.id_copie
            WHERE cp.id_matiere = ? AND a.id IS NULL
        ) as subquery
        LIMIT ?
    ");
    $copies->bindValue(1, $id_matiere, PDO::PARAM_INT);
    $copies->bindValue(2, $nb_copies, PDO::PARAM_INT);
    $copies->execute();
    $copies_a_attribuer = $copies->fetchAll();

    // Attribuer les copies
    if (count($copies_a_attribuer) > 0) {
        $stmt = $pdo->prepare("INSERT INTO attribution (id_correcteur, id_copie) VALUES (?, ?)");
        foreach ($copies_a_attribuer as $copie) {
            $stmt->execute([$id_correcteur, $copie['id']]);
        }
        $_SESSION['message'] = count($copies_a_attribuer) . " copie(s) ont été attribuées avec succès.";
    } else {
        $_SESSION['message'] = "Aucune copie n'a pu être attribuée.";
    }
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit();
}

// Afficher le message s'il existe
$message = isset($_SESSION['message']) ? $_SESSION['message'] : '';
unset($_SESSION['message']);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Attribution des Copies</title>
    <link rel="stylesheet" href="../public/css/style.css">
    
</head>
<body>
    <h2>Attribution des Copies</h2>
    <a href="dashboard.php" class="back-link">← Retour</a>

    <?php if ($message): ?>
        <div class="alert alert-success" style="background-color: #d4edda; color: #155724; padding: 10px; margin: 10px 0; border-radius: 4px; border: 1px solid #c3e6cb;">
            <?= htmlspecialchars($message) ?>
        </div>
    <?php endif; ?>

    <div class="attribution-form">
        <h3>Attribuer des Copies</h3>
        <form method="POST">
            <div>
                <label>Correcteur:</label>
                <select name="correcteur" required>
                    <?php foreach ($correcteurs as $c): ?>
                        <option value="<?= $c['id'] ?>">
                            <?= htmlspecialchars($c['prenom'] . ' ' . $c['nom']) ?>
                            (<?= htmlspecialchars($c['specialite']) ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div>
                <label>Matière:</label>
                <select name="matiere" required>
                    <?php foreach ($matieres as $m): ?>
                        <option value="<?= $m['id'] ?>">
                            <?= htmlspecialchars($m['nom']) ?>
                            (<?= htmlspecialchars($m['concours_nom']) ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div>
                <label>Nombre de copies:</label>
                <input type="number" name="nb_copies" min="1" value="1" required>
            </div>
            <button type="submit">Attribuer</button>
        </form>
    </div>

    <div class="correcteur-list">
        <h3>Liste des Correcteurs</h3>
        <?php foreach ($correcteurs as $c): ?>
            <div class="correcteur-item">
                <strong><?= htmlspecialchars($c['prenom'] . ' ' . $c['nom']) ?></strong>
                <span class="specialite">Spécialité: <?= htmlspecialchars($c['specialite']) ?></span>
            </div>
        <?php endforeach; ?>
    </div>
</body>
</html>
<style>
        .attribution-form {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .correcteur-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .correcteur-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .specialite {
            color: #666;
            font-style: italic;
        }
    </style>
