<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';
verifier_connexion();
verifier_role('admin');

// Supprimer un concours (et ses matières associées)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['supprimer_id'])) {
    $id = intval($_POST['supprimer_id']);

    // Supprimer les matières associées
    $pdo->prepare("DELETE FROM matiere WHERE id_concours = ?")->execute([$id]);

    // Supprimer le concours lui-même
    $pdo->prepare("DELETE FROM concours WHERE id = ?")->execute([$id]);

    header('Location: concours.php');
    exit;
}

// Récupération des concours et de leurs matières associées
$concours = $pdo->query("SELECT * FROM concours ORDER BY date_debut DESC")->fetchAll();

// Récupérer les matières groupées par concours
$matiere_stmt = $pdo->query("SELECT id_concours, GROUP_CONCAT(nom SEPARATOR ', ') AS matieres FROM matiere GROUP BY id_concours");
$matieres_par_concours = [];
foreach ($matiere_stmt as $row) {
    $matieres_par_concours[$row['id_concours']] = $row['matieres'];
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Gestion des concours</title>
    <link rel="stylesheet" href="../public/css/style.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f6f9;
            margin: 0;
            padding: 0;
            color: #333;
        }

        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            letter-spacing: 1px;
        }

        .container {
            padding: 20px;
        }

        h2, h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        form.ajout-form {
            background-color: #ffffff;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 20px;
            max-width: 600px;
        }

        input[type="text"], input[type="date"], textarea {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }

        button {
            background-color: #007bff;
            color: #fff;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }

        button:hover {
            background-color: #0056b3;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }

        thead {
            background-color: #3498db;
            color: white;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        tr:hover {
            background-color: #f1f1f1;
        }

        .actions form {
            display: inline;
        }

        .btn-delete {
            background-color: #dc3545;
            color: white;
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .btn-delete:hover {
            background-color: #c82333;
        }

        a {
            display: inline-block;
            margin-top: 15px;
            color: #3498db;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
<header>GESTION DES CONCOURS</header>

<div class="container">
    <h2>Ajouter un concours</h2>
    <form method="POST" class="ajout-form">
        <input type="hidden" name="action" value="ajouter">
        <input type="text" name="titre" placeholder="Titre" required>
        <input type="date" name="date_debut" required>
        <input type="date" name="date_fin" required>
        <textarea name="description" placeholder="Description (optionnelle)"></textarea>
        <button type="submit">Ajouter</button>
    </form>

    <?php
    // Traitement d'ajout d'un concours
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'ajouter') {
        if (!empty($_POST['titre']) && !empty($_POST['date_debut']) && !empty($_POST['date_fin'])) {
            $stmt = $pdo->prepare("INSERT INTO concours (titre, date_debut, date_fin, description) VALUES (?, ?, ?, ?)");
            $stmt->execute([
                $_POST['titre'],
                $_POST['date_debut'],
                $_POST['date_fin'],
                $_POST['description'] ?? null
            ]);
            header('Location: concours.php');
            exit;
        } else {
            echo "<p style='color: red;'>Veuillez remplir tous les champs obligatoires.</p>";
        }
    }
    ?>

    <h3>Liste des concours</h3>
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Titre</th>
                <th>Date Début</th>
                <th>Date Fin</th>
                <th>Description</th>
                <th>Matières</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($concours as $c): ?>
                <tr>
                    <td><?= $c['id'] ?></td>
                    <td><?= htmlspecialchars($c['titre']) ?></td>
                    <td><?= $c['date_debut'] ?></td>
                    <td><?= $c['date_fin'] ?></td>
                    <td><?= nl2br(htmlspecialchars($c['description'])) ?></td>
                    <td>
                        <?= isset($matieres_par_concours[$c['id']]) ? htmlspecialchars($matieres_par_concours[$c['id']]) : '<em>Aucune matière</em>' ?>
                    </td>
                    <td class="actions">
                        <form method="POST" onsubmit="return confirm('Voulez-vous vraiment supprimer ce concours ?');">
                            <input type="hidden" name="supprimer_id" value="<?= $c['id'] ?>">
                            <button type="submit" class="btn-delete">Supprimer</button>
                        </form>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <a href="ajouter_matiere.php">Ajouter une matière à un concours</a><br>
    <a href="dashboard.php">← Retour au tableau de bord</a>
</div>
</body>
</html>
