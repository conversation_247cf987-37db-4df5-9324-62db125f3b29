<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';

verifier_connexion();
verifier_role('admin');

$page_title = "Copies déposées";
require_once '../includes/header.php';

// Récupération des concours pour la liste déroulante
$concours_stmt = $pdo->query("SELECT id, titre FROM concours");
$concours_list = $concours_stmt->fetchAll();

// Récupération des filtres GET
$filtre_concours = $_GET['concours_id'] ?? '';
$filtre_nom = $_GET['recherche_nom'] ?? '';

// Construction dynamique de la requête SQL
$sql = "SELECT c.id, c.fichier_path, c.date_depot, 
               u.nom, u.prenom, 
               con.titre AS concours
        FROM copie c
        JOIN users u ON c.id_candidat = u.id
        JOIN concours con ON c.id_concours = con.id
        WHERE 1=1";

$params = [];

if (!empty($filtre_concours)) {
    $sql .= " AND c.id_concours = :concours_id";
    $params[':concours_id'] = $filtre_concours;
}

if (!empty($filtre_nom)) {
    $sql .= " AND (u.nom LIKE :nom OR u.prenom LIKE :nom)";
    $params[':nom'] = '%' . $filtre_nom . '%';
}

$sql .= " ORDER BY c.date_depot DESC";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$copies = $stmt->fetchAll();
?>
<h2>Liste des copies déposées</h2>

<!-- Formulaire de filtre -->
<form method="GET" class="filter-form">
    <select name="concours_id">
        <option value="">-- Tous les concours --</option>
        <?php foreach ($concours_list as $concours): ?>
            <option value="<?= e($concours['id']) ?>" <?= ($filtre_concours == $concours['id']) ? 'selected' : '' ?>>
                <?= e($concours['titre']) ?>
            </option>
        <?php endforeach; ?>
    </select>

    <input type="text" name="recherche_nom" placeholder="Nom ou prénom"
           value="<?= e($filtre_nom) ?>">

    <button type="submit">Filtrer</button>
</form>

<table border="1" cellpadding="10" cellspacing="0" style="width: 100%; background-color: #f8f8f8;">
    <thead style="background-color: #d0e0f0;">
        <tr>
            <th>#</th>
            <th>Candidat</th>
            <th>Concours</th>
            <th>Date de dépôt</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        <?php if (count($copies) > 0): ?>
            <?php foreach ($copies as $copie): ?>
                <tr>
                    <td><?= e($copie['id']) ?></td>
                    <td><?= e($copie['prenom']) . ' ' . e($copie['nom']) ?></td>
                    <td><?= e($copie['concours']) ?></td>
                    <td><?= formater_date($copie['date_depot']) ?></td>
                    <td>
                        📄 <a href="/Gestions_concours/public/uploads/<?= e($copie['fichier_path']) ?>" target="_blank">Voir</a> |
                        ⬇️ <a href="/Gestions_concours/public/uploads/<?= e($copie['fichier_path']) ?>" download>Télécharger</a><br>
                        🔍 <button onclick="togglePreview('preview<?= $copie['id'] ?>')">Aperçu</button>
                    </td>
                </tr>
                <tr id="preview<?= $copie['id'] ?>" style="display: none;">
                    <td colspan="5" style="text-align: center;">
                        <iframe src="/Gestions_concours/public/uploads/<?= e($copie['fichier_path']) ?>" width="80%" height="400px" style="border: 1px solid #ccc;"></iframe>
                    </td>
                </tr>
            <?php endforeach; ?>
        <?php else: ?>
            <tr>
                <td colspan="5" style="text-align: center;">Aucune copie trouvée avec ce filtre.</td>
            </tr>
        <?php endif; ?>
    </tbody>
</table>

<script>
function togglePreview(id) {
    const element = document.getElementById(id);
    element.style.display = (element.style.display === "none") ? "table-row" : "none";
}
</script>

<?php require_once '../includes/footer.php'; ?>

<style>
.filter-form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 15px 0;
}
.filter-form select, .filter-form input {
    padding: 6px;
    border-radius: 5px;
    border: 1px solid #ccc;
}
.filter-form button {
    padding: 6px 15px;
    border: none;
    background-color: #007bff;
    color: white;
    border-radius: 5px;
}
</style>
