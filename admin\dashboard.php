<?php 
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';

verifier_connexion();
verifier_role('admin');

$page_title = "Dashboard Admin";
require_once '../includes/header.php';

$stmt = $pdo->query("SELECT COUNT(*) AS total_candidats FROM users WHERE role = 'candidat'");
$candidats = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT COUNT(*) AS total_correcteurs FROM users WHERE role = 'correcteur'");
$correcteurs = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT COUNT(*) AS total_concours FROM concours");
$concours = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT COUNT(*) AS total_copies FROM copie");
$copies = $stmt->fetchColumn();
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Admin - Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link rel="stylesheet" href="../public/css/style.css">
    <style>
        body {
            background-color: #121212;
            color: #f1f1f1;
            font-family: 'Segoe UI', sans-serif;
            margin: 0;
            padding: 0;
        }

        h2 {
            color: #00bcd4;
            text-align: center;
            margin-top: 30px;
            font-size: 2em;
        }
        table {
            width: 90%;
            max-width: 800px;
            margin: 30px auto;
            border-collapse: collapse;
            background-color: #1e1e1e;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 0 15px rgba(0, 188, 212, 0.2);
        }

        th, td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid #333;
        }
        th {
            background-color: #00bcd4;
            color: #121212;
            font-size: 1.1em;
        }

        td i {
            margin-right: 10px;
            color: #00bcd4;
        }
        tr:last-child td {
            border-bottom: none;
        }
        a {
            display: inline-block;
            margin: 20px auto;
            padding: 12px 20px;
            background-color: #00bcd4;
            color: #121212;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        a:hover {
            background-color: #008c9e;
            color: #fff;
        }
        p {
            text-align: center;
        }

        @media (max-width: 768px) {
            h2 {
                font-size: 1.5em;
            }
            table, th, td {
                font-size: 14px;
            }
            a {
                padding: 10px 15px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <h2>Tableau de bord Administrateur</h2>
    <table>
        <thead>
            <tr>
                <th>Statistique</th>
                <th>Valeur</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td><i class="fas fa-user-graduate"></i> Total candidats</td>
                <td><?= $candidats ?></td>
            </tr>
            <tr>
                <td><i class="fas fa-chalkboard-teacher"></i> Total correcteurs</td>
                <td><?= $correcteurs ?></td>
            </tr>
            <tr>
                <td><i class="fas fa-award"></i> Total concours</td>
                <td><?= $concours ?></td>
            </tr>
            <tr>
                <td><i class="fas fa-file-alt"></i> Total copies déposées</td>
                <td><?= $copies ?></td>
            </tr>
        </tbody>
    </table>

    <p>
        <a href="grille_evaluation.php"><i class="fas fa-clipboard-list"></i> Critères de correction</a>
        <a href="attributions.php"><i class="fas fa-random"></i> Attribuer des copies</a>
        <a href="resultats.php"><i class="fas fa-chart-bar"></i> Résultats et Publications</a>
    </p>
</body>
</html>

<?php require_once '../includes/footer.php'; ?>
