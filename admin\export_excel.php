<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';
verifier_connexion();
verifier_role('admin');

header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment;filename="resultats_concours.xls"');

// Requête pour récupérer tous les concours avec leurs matières et résultats
$query = "
    SELECT 
        c.id AS concours_id,
        c.titre AS concours_titre,
        m.id AS matiere_id,
        m.nom_matiere,
        m.coefficient,
        cp.identifiant_anon,
        u.nom AS candidat_nom,
        u.prenom AS candidat_prenom,
        corr.note_total,
        corr.date_correction
    FROM concours c
    JOIN matieres m ON m.id_concours = c.id
    JOIN copie cp ON cp.id_concours = c.id
    JOIN users u ON cp.id_candidat = u.id AND u.role = 'candidat'
    JOIN attribution a ON a.id_copie = cp.id AND a.id_matiere = m.id
    JOIN correction corr ON corr.id_attribution = a.id
    ORDER BY c.titre, m.nom_matiere, u.nom, u.prenom
";

$stmt = $pdo->prepare($query);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Organiser les données par concours et matière
$organizedData = [];
foreach ($results as $row) {
    $concoursId = $row['concours_id'];
    $matiereId = $row['matiere_id'];
    
    if (!isset($organizedData[$concoursId])) {
        $organizedData[$concoursId] = [
            'titre' => $row['concours_titre'],
            'matieres' => []
        ];
    }
    
    if (!isset($organizedData[$concoursId]['matieres'][$matiereId])) {
        $organizedData[$concoursId]['matieres'][$matiereId] = [
            'nom_matiere' => $row['nom_matiere'],
            'coefficient' => $row['coefficient'],
            'candidats' => []
        ];
    }
    
    $organizedData[$concoursId]['matieres'][$matiereId]['candidats'][] = [
        'identifiant' => $row['identifiant_anon'],
        'nom' => $row['candidat_nom'],
        'prenom' => $row['candidat_prenom'],
        'note' => $row['note_total'],
        'date' => $row['date_correction']
    ];
}

// Génération du fichier Excel
echo '<meta charset="UTF-8">';
foreach ($organizedData as $concours) {
    echo '<h2>'.htmlspecialchars($concours['titre']).'</h2>';
    
    foreach ($concours['matieres'] as $matiere) {
        echo '<h3>'.htmlspecialchars($matiere['nom_matiere']).' (Coeff: '.$matiere['coefficient'].')</h3>';
        
        echo '<table border="1">
                <tr>
                    <th>Identifiant</th>
                    <th>Nom</th>
                    <th>Prénom</th>
                    <th>Note/20</th>
                    <th>Date correction</th>
                </tr>';
        
        foreach ($matiere['candidats'] as $candidat) {
            echo '<tr>
                    <td>'.htmlspecialchars($candidat['identifiant']).'</td>
                    <td>'.htmlspecialchars($candidat['nom']).'</td>
                    <td>'.htmlspecialchars($candidat['prenom']).'</td>
                    <td>'.htmlspecialchars($candidat['note']).'</td>
                    <td>'.htmlspecialchars($candidat['date']).'</td>
                  </tr>';
        }
        
        echo '</table><br>';
    }
    echo '<br>';
}

exit();
?>