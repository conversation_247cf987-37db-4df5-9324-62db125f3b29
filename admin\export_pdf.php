<?php
require_once __DIR__ . '/../vendor/tecnickcom/tcpdf/tcpdf.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';

verifier_connexion();
verifier_role('admin');

// Créer une nouvelle instance de TCPDF
$pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

// Définir les informations du document
$pdf->SetCreator(PDF_CREATOR);
$pdf->SetAuthor('Système de Gestion des Concours');
$pdf->SetTitle('Liste des Attributions');

// Définir les marges
$pdf->SetMargins(15, 15, 15);
$pdf->SetHeaderMargin(5);
$pdf->SetFooterMargin(10);

// Ajouter une page
$pdf->AddPage();

// Définir le contenu
$pdf->SetFont('helvetica', '', 12);

// Titre
$pdf->Cell(0, 10, 'Liste des Attributions de Copies', 0, 1, 'C');
$pdf->Ln(10);

// En-têtes du tableau
$pdf->SetFont('helvetica', 'B', 10);
$pdf->Cell(60, 7, 'Correcteur', 1);
$pdf->Cell(60, 7, 'Matière', 1);
$pdf->Cell(40, 7, 'Nombre de copies', 1);
$pdf->Ln();

// Contenu du tableau
$pdf->SetFont('helvetica', '', 10);

// Récupérer les données
$stmt = $pdo->query("
    SELECT 
        u.nom as correcteur_nom,
        u.prenom as correcteur_prenom,
        m.nom as matiere_nom,
        COUNT(a.id) as nb_copies
    FROM attribution a
    JOIN users u ON a.id_correcteur = u.id
    JOIN copie c ON a.id_copie = c.id
    JOIN matiere m ON c.id_matiere = m.id
    GROUP BY u.id, m.id
    ORDER BY u.nom, m.nom
");

while ($row = $stmt->fetch()) {
    $pdf->Cell(60, 7, $row['correcteur_prenom'] . ' ' . $row['correcteur_nom'], 1);
    $pdf->Cell(60, 7, $row['matiere_nom'], 1);
    $pdf->Cell(40, 7, $row['nb_copies'], 1);
    $pdf->Ln();
}

// Générer le PDF
$pdf->Output('attributions.pdf', 'I');
