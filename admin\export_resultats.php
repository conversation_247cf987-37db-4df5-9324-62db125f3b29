<?php
require_once __DIR__ . '/../vendor/tecnickcom/tcpdf/tcpdf.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';

verifier_connexion();
verifier_role('admin');

if (!isset($_GET['concours_id'])) {
    die('ID du concours non spécifié');
}

$concours_id = intval($_GET['concours_id']);

// Récupérer les informations du concours
$stmt = $pdo->prepare("SELECT titre FROM concours WHERE id = ?");
$stmt->execute([$concours_id]);
$concours = $stmt->fetch();

if (!$concours) {
    die('Concours non trouvé');
}

// Récupérer les résultats
$stmt = $pdo->prepare("
    SELECT 
        c.identifiant_anon,
        u.nom as candidat_nom,
        u.prenom as candidat_prenom,
        m.nom as matiere_nom,
        corr.note_total,
        corr.commentaire_global,
        corr.date_correction,
        uc.nom as correcteur_nom,
        uc.prenom as correcteur_prenom
    FROM copie c
    JOIN users u ON c.id_candidat = u.id
    JOIN matiere m ON c.id_matiere = m.id
    LEFT JOIN attribution a ON c.id = a.id_copie
    LEFT JOIN correction corr ON a.id = corr.id_attribution
    LEFT JOIN users uc ON a.id_correcteur = uc.id
    WHERE m.id_concours = ?
    ORDER BY u.nom, u.prenom, m.nom
");
$stmt->execute([$concours_id]);
$resultats = $stmt->fetchAll();

// Créer le PDF en orientation paysage pour plus d'espace
$pdf = new TCPDF('L', PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

// Définir les informations du document
$pdf->SetCreator(PDF_CREATOR);
$pdf->SetAuthor('Système de Gestion des Concours');
$pdf->SetTitle('Résultats - ' . $concours['titre']);

// Définir les marges
$pdf->SetMargins(10, 15, 10); // Marges de 10mm à gauche et à droite
$pdf->SetHeaderMargin(5);
$pdf->SetFooterMargin(10);

// Ajouter une page
$pdf->AddPage();

// Définir le contenu
$pdf->SetFont('helvetica', 'B', 16);
$pdf->Cell(0, 10, 'Résultats - ' . $concours['titre'], 0, 1, 'C');
$pdf->Ln(8);

// En-têtes du tableau avec nouvelles largeurs optimisées
$header_widths = [
    'ID Anonyme' => 35,
    'Candidat' => 40,
    'Matière' => 25,
    'Note' => 15,
    'Correcteur' => 40,
    'Date' => 20,
    'Commentaire' => 102 // Largeur explicite pour le commentaire
];

$pdf->SetFont('helvetica', 'B', 9);
foreach ($header_widths as $label => $width) {
    $pdf->Cell($width, 7, $label, 1, 0, 'C'); // Centré pour les en-têtes
}
$pdf->Ln();

// Données du tableau avec gestion des textes longs
$pdf->SetFont('helvetica', '', 8);
foreach ($resultats as $r) {
    $candidat_text = htmlspecialchars($r['candidat_prenom'] . ' ' . $r['candidat_nom']);
    $matiere_text = htmlspecialchars($r['matiere_nom']);
    $note_text = $r['note_total'] ? number_format($r['note_total'], 2) : 'Non corrigé';
    $correcteur_text = (!empty($r['correcteur_prenom']) || !empty($r['correcteur_nom'])) ? 
        htmlspecialchars($r['correcteur_prenom'] . ' ' . $r['correcteur_nom']) : 'Non attribué';
    $date_text = $r['date_correction'] ? date('d/m/Y', strtotime($r['date_correction'])) : '-';
    $commentaire_text = htmlspecialchars($r['commentaire_global'] ?? '');

    $current_x = $pdf->GetX();
    $current_y = $pdf->GetY();

    // Calculate required height for the 'Commentaire' MultiCell using a transaction
    $pdf->startTransaction();
    $pdf->MultiCell($header_widths['Commentaire'], 0, $commentaire_text, 0, 'L', 0, 1, '', '', true, 1, false, true);
    $comment_height_needed = $pdf->GetY() - $current_y;
    $pdf->rollbackTransaction(true);

    $row_height = max(7, $comment_height_needed); // Use 7mm as minimum height for a row

    // Check for page break BEFORE drawing the row
    if (($pdf->GetY() + $row_height) > ($pdf->getPageHeight() - $pdf->getBreakMargin())) {
        $pdf->AddPage();
        // Redraw headers on new page
        $pdf->SetFont('helvetica', 'B', 9);
        foreach ($header_widths as $label => $width) {
            $pdf->Cell($width, 7, $label, 1, 0, 'C');
        }
        $pdf->Ln();
        $pdf->SetFont('helvetica', '', 8); // Reset font for data
    }

    // Print cells for the current row
    $x_offset = $pdf->GetX();
    $current_y = $pdf->GetY();

    // ID Anonyme
    $pdf->SetXY($x_offset, $current_y);
    $pdf->Cell($header_widths['ID Anonyme'], $row_height, $r['identifiant_anon'], 1, 0, 'L', 0, '', 0, true);
    $x_offset += $header_widths['ID Anonyme'];

    // Candidat
    $pdf->SetXY($x_offset, $current_y);
    $pdf->Cell($header_widths['Candidat'], $row_height, $candidat_text, 1, 0, 'L', 0, '', 0, true);
    $x_offset += $header_widths['Candidat'];

    // Matière
    $pdf->SetXY($x_offset, $current_y);
    $pdf->Cell($header_widths['Matière'], $row_height, $matiere_text, 1, 0, 'L', 0, '', 0, true);
    $x_offset += $header_widths['Matière'];

    // Note
    $pdf->SetXY($x_offset, $current_y);
    $pdf->Cell($header_widths['Note'], $row_height, $note_text, 1, 0, 'C', 0, '', 0, true);
    $x_offset += $header_widths['Note'];

    // Correcteur
    $pdf->SetXY($x_offset, $current_y);
    $pdf->Cell($header_widths['Correcteur'], $row_height, $correcteur_text, 1, 0, 'L', 0, '', 0, true);
    $x_offset += $header_widths['Correcteur'];

    // Date
    $pdf->SetXY($x_offset, $current_y);
    $pdf->Cell($header_widths['Date'], $row_height, $date_text, 1, 0, 'C', 0, '', 0, true);
    $x_offset += $header_widths['Date'];

    // Commentaire (MultiCell)
    $pdf->SetXY($x_offset, $current_y);
    $pdf->MultiCell($header_widths['Commentaire'], $row_height, $commentaire_text, 1, 'L', 0, 1, '', '', true, 1, false, true, $row_height, 'T');
}

// Générer le PDF
$pdf->Output('resultats_' . $concours_id . '.pdf', 'I');