<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';
verifier_connexion();
verifier_role('admin');

$success = '';
$error = '';

// Traitement des actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'ajouter_critere':
                $id_concours = intval($_POST['id_concours']);
                $critere = trim($_POST['critere']);
                $bareme = intval($_POST['bareme']);
                
                if ($id_concours && $critere && $bareme > 0) {
                    try {
                        $stmt = $pdo->prepare("INSERT INTO grille_evaluation (id_concours, critere, bareme) VALUES (?, ?, ?)");
                        $stmt->execute([$id_concours, $critere, $bareme]);
                        $success = "Critère ajouté avec succès.";
                    } catch (Exception $e) {
                        $error = "Erreur lors de l'ajout : " . $e->getMessage();
                    }
                } else {
                    $error = "Veuillez remplir tous les champs correctement.";
                }
                break;
                
            case 'supprimer_critere':
                $id_critere = intval($_POST['id_critere']);
                try {
                    $stmt = $pdo->prepare("DELETE FROM grille_evaluation WHERE id = ?");
                    $stmt->execute([$id_critere]);
                    $success = "Critère supprimé avec succès.";
                } catch (Exception $e) {
                    $error = "Erreur lors de la suppression : " . $e->getMessage();
                }
                break;
                
            case 'modifier_critere':
                $id_critere = intval($_POST['id_critere']);
                $critere = trim($_POST['critere']);
                $bareme = intval($_POST['bareme']);
                
                if ($id_critere && $critere && $bareme > 0) {
                    try {
                        $stmt = $pdo->prepare("UPDATE grille_evaluation SET critere = ?, bareme = ? WHERE id = ?");
                        $stmt->execute([$critere, $bareme, $id_critere]);
                        $success = "Critère modifié avec succès.";
                    } catch (Exception $e) {
                        $error = "Erreur lors de la modification : " . $e->getMessage();
                    }
                } else {
                    $error = "Veuillez remplir tous les champs correctement.";
                }
                break;
        }
    }
}

// Récupération des concours
$concours = $pdo->query("SELECT id, titre FROM concours ORDER BY titre")->fetchAll();

// Récupération des critères par concours
$criteres_par_concours = [];
foreach ($concours as $c) {
    $stmt = $pdo->prepare("
        SELECT ge.*, c.titre as concours_titre 
        FROM grille_evaluation ge 
        JOIN concours c ON ge.id_concours = c.id 
        WHERE ge.id_concours = ? 
        ORDER BY ge.id
    ");
    $stmt->execute([$c['id']]);
    $criteres_par_concours[$c['id']] = $stmt->fetchAll();
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Gestion des Critères de Correction</title>
    <link rel="stylesheet" href="../public/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            color: #ffffff;
            text-align: center;
            margin-bottom: 40px;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section h2::before {
            content: "📋";
            font-size: 1.5rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group select,
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-family: inherit;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group select:focus,
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(231, 76, 60, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(243, 156, 18, 0.3);
        }

        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .concours-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border-left: 4px solid #667eea;
        }

        .concours-card h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .critere-item {
            background: rgba(248, 249, 250, 0.8);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .critere-info {
            flex: 1;
        }

        .critere-actions {
            display: flex;
            gap: 10px;
        }

        .bareme-badge {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            margin-top: 30px;
            padding: 12px 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.2);
            transform: translateX(-5px);
        }

        .form-inline {
            display: flex;
            gap: 15px;
            align-items: end;
        }

        .form-inline .form-group {
            flex: 1;
        }

        .no-criteres {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 20px;
        }

        @media (max-width: 768px) {
            .form-inline {
                flex-direction: column;
            }
            
            .critere-item {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }
            
            .critere-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Gestion des Critères de Correction</h1>
        
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <!-- Formulaire d'ajout -->
        <div class="section">
            <h2>Ajouter un critère de correction</h2>
            <form method="POST" class="form-inline">
                <input type="hidden" name="action" value="ajouter_critere">
                
                <div class="form-group">
                    <label for="id_concours">Concours</label>
                    <select name="id_concours" id="id_concours" required>
                        <option value="">-- Sélectionner un concours --</option>
                        <?php foreach ($concours as $c): ?>
                            <option value="<?= $c['id'] ?>"><?= htmlspecialchars($c['titre']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="critere">Critère</label>
                    <input type="text" name="critere" id="critere" placeholder="Ex: Clarté de l'expression" required>
                </div>
                
                <div class="form-group">
                    <label for="bareme">Barème (points)</label>
                    <input type="number" name="bareme" id="bareme" min="1" max="20" value="5" required>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Ajouter
                    </button>
                </div>
            </form>
        </div>

        <!-- Liste des critères par concours -->
        <div class="section">
            <h2>Critères existants</h2>
            
            <?php if (empty($concours)): ?>
                <div class="no-criteres">
                    <i class="fas fa-info-circle"></i> Aucun concours disponible. Créez d'abord un concours.
                </div>
            <?php else: ?>
                <?php foreach ($concours as $c): ?>
                    <div class="concours-card">
                        <h3>
                            <i class="fas fa-trophy"></i> <?= htmlspecialchars($c['titre']) ?>
                            <?php 
                            $total_bareme = array_sum(array_column($criteres_par_concours[$c['id']], 'bareme'));
                            if ($total_bareme > 0): 
                            ?>
                                <span class="bareme-badge">Total: <?= $total_bareme ?> points</span>
                            <?php endif; ?>
                        </h3>
                        
                        <?php if (empty($criteres_par_concours[$c['id']])): ?>
                            <div class="no-criteres">
                                <i class="fas fa-exclamation-circle"></i> Aucun critère défini pour ce concours
                            </div>
                        <?php else: ?>
                            <?php foreach ($criteres_par_concours[$c['id']] as $critere): ?>
                                <div class="critere-item">
                                    <div class="critere-info">
                                        <strong><?= htmlspecialchars($critere['critere']) ?></strong>
                                        <span class="bareme-badge"><?= $critere['bareme'] ?> pts</span>
                                    </div>
                                    <div class="critere-actions">
                                        <button type="button" class="btn btn-warning btn-sm" onclick="editCritere(<?= $critere['id'] ?>, '<?= htmlspecialchars($critere['critere'], ENT_QUOTES) ?>', <?= $critere['bareme'] ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer ce critère ?')">
                                            <input type="hidden" name="action" value="supprimer_critere">
                                            <input type="hidden" name="id_critere" value="<?= $critere['id'] ?>">
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <a href="dashboard.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Retour au tableau de bord
        </a>
    </div>

    <!-- Modal de modification -->
    <div id="editModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 15px; min-width: 400px;">
            <h3 style="margin-bottom: 20px; color: #2c3e50;">Modifier le critère</h3>
            <form method="POST" id="editForm">
                <input type="hidden" name="action" value="modifier_critere">
                <input type="hidden" name="id_critere" id="edit_id_critere">
                
                <div class="form-group">
                    <label for="edit_critere">Critère</label>
                    <input type="text" name="critere" id="edit_critere" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_bareme">Barème (points)</label>
                    <input type="number" name="bareme" id="edit_bareme" min="1" max="20" required>
                </div>
                
                <div style="display: flex; gap: 10px; justify-content: end;">
                    <button type="button" class="btn btn-secondary" onclick="closeEditModal()">Annuler</button>
                    <button type="submit" class="btn btn-primary">Modifier</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function editCritere(id, critere, bareme) {
            document.getElementById('edit_id_critere').value = id;
            document.getElementById('edit_critere').value = critere;
            document.getElementById('edit_bareme').value = bareme;
            document.getElementById('editModal').style.display = 'block';
        }

        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        // Fermer le modal en cliquant à l'extérieur
        document.getElementById('editModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeEditModal();
            }
        });
    </script>
</body>
</html>
