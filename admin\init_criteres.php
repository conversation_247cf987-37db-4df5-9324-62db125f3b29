<?php
require_once __DIR__ . '/../config/database.php';

try {
    // Vérifier s'il y a des concours
    $concours = $pdo->query('SELECT id, titre FROM concours ORDER BY titre')->fetchAll();
    
    if (empty($concours)) {
        echo "Création d'un concours de test...\n";
        $stmt = $pdo->prepare('INSERT INTO concours (titre, date_debut, date_fin, description) VALUES (?, ?, ?, ?)');
        $stmt->execute([
            'Concours de Mathématiques 2024',
            '2024-01-15',
            '2024-01-30',
            'Concours de mathématiques niveau universitaire'
        ]);
        $id_concours = $pdo->lastInsertId();
        echo "Concours créé avec ID: $id_concours\n";
    } else {
        echo "Concours existants:\n";
        foreach ($concours as $c) {
            echo "- ID: {$c['id']} - {$c['titre']}\n";
        }
        $id_concours = $concours[0]['id'];
    }

    // Vérifier s'il y a déjà des critères
    $criteres = $pdo->prepare('SELECT COUNT(*) FROM grille_evaluation WHERE id_concours = ?');
    $criteres->execute([$id_concours]);
    $nb_criteres = $criteres->fetchColumn();

    if ($nb_criteres == 0) {
        echo "Ajout de critères de correction par défaut...\n";
        $criteres_defaut = [
            ['Exactitude des calculs', 8],
            ['Méthode et raisonnement', 6],
            ['Clarté de la présentation', 4],
            ['Justification des étapes', 2]
        ];
        
        $stmt = $pdo->prepare('INSERT INTO grille_evaluation (id_concours, critere, bareme) VALUES (?, ?, ?)');
        foreach ($criteres_defaut as $crit) {
            $stmt->execute([$id_concours, $crit[0], $crit[1]]);
            echo "✓ Critère ajouté: {$crit[0]} ({$crit[1]} pts)\n";
        }
        echo "Critères de correction créés avec succès!\n";
    } else {
        echo "Il y a déjà $nb_criteres critère(s) pour ce concours.\n";
    }
    
    echo "\nTerminé! Vous pouvez maintenant tester la correction des copies.\n";
    
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}
?>
