<?php 
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';
verifier_connexion();
verifier_role('admin');

// Traitement ajout / modification / suppression
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $id = intval($_POST['id'] ?? 0);
    $nom = trim($_POST['nom'] ?? '');
    $coefficient = floatval($_POST['coefficient'] ?? 1);
    $id_concours = intval($_POST['id_concours'] ?? 0);

    if ($action === 'ajouter' && $nom && $coefficient && $id_concours) {
        $stmt = $pdo->prepare("INSERT INTO matiere (nom, coefficient, id_concours) VALUES (?, ?, ?)");
        $stmt->execute([$nom, $coefficient, $id_concours]);
    } elseif ($action === 'modifier' && $id && $nom && $coefficient && $id_concours) {
        $stmt = $pdo->prepare("UPDATE matiere SET nom = ?, coefficient = ?, id_concours = ? WHERE id = ?");
        $stmt->execute([$nom, $coefficient, $id_concours, $id]);
    } elseif ($action === 'supprimer' && $id) {
        $stmt = $pdo->prepare("DELETE FROM matiere WHERE id = ?");
        $stmt->execute([$id]);
    }
}

// Récupération
$matieres = $pdo->query("
    SELECT m.*, c.titre as concours_nom 
    FROM matiere m 
    JOIN concours c ON m.id_concours = c.id 
    ORDER BY c.titre, m.nom
")->fetchAll();

$concours = $pdo->query("SELECT id, titre FROM concours ORDER BY titre")->fetchAll();
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Gestion des Matières</title>
    <link rel="stylesheet" href="../public/css/style.css">
</head>
<body>

<h2>Gestion des Matières</h2>
<a href="dashboard.php" class="back-link">← Retour</a>

<!-- Formulaire d’ajout -->
<div class="matiere-form">
    <h3>Ajouter une matière</h3>
    <form method="POST">
        <input type="hidden" name="action" value="ajouter">
        <label>Concours :</label>
        <select name="id_concours" required>
            <?php foreach ($concours as $c): ?>
                <option value="<?= $c['id'] ?>"><?= htmlspecialchars($c['titre']) ?></option>
            <?php endforeach; ?>
        </select>
        <label>Nom :</label>
        <input type="text" name="nom" required>
        <label>Coefficient :</label>
        <input type="number" name="coefficient" min="0.1" step="0.1" value="1" required>
        <button type="submit" class="btn-save">Ajouter</button>
    </form>
</div>

<!-- Liste des matières -->
<div class="matiere-list">
    <h3>Liste des matières</h3>
    <?php foreach ($matieres as $m): ?>
        <div class="matiere-item" id="matiere-<?= $m['id'] ?>">
            <form method="POST" class="inline-form">
                <input type="hidden" name="action" value="modifier">
                <input type="hidden" name="id" value="<?= $m['id'] ?>">
                <input type="text" name="nom" value="<?= htmlspecialchars($m['nom']) ?>" required>
                <input type="number" name="coefficient" step="0.1" min="0.1" value="<?= $m['coefficient'] ?>" required>
                <select name="id_concours" required>
                    <?php foreach ($concours as $c): ?>
                        <option value="<?= $c['id'] ?>" <?= $c['id'] == $m['id_concours'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($c['titre']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <button type="submit" class="btn-action btn-edit">Enregistrer</button>
            </form>

            <form method="POST" onsubmit="return confirm('Confirmer la suppression ?');" style="margin-left: 10px;">
                <input type="hidden" name="action" value="supprimer">
                <input type="hidden" name="id" value="<?= $m['id'] ?>">
                <button type="submit" class="btn-action btn-delete">Supprimer</button>
            </form>
        </div>
    <?php endforeach; ?>
</div>
</body>
</html>
<style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f4f6f9;
        }
        h2 {
            text-align: center;
        }
        .matiere-form, .matiere-list {
            background: white;
            padding: 20px;
            margin: 20px auto;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 700px;
        }
        .matiere-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .matiere-item:last-child {
            border-bottom: none;
        }
        form.inline-form {
            display: flex;
            gap: 5px;
            align-items: center;
        }
        form.inline-form input,
        form.inline-form select {
            padding: 4px;
        }
        .btn-action {
            padding: 6px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            border: none;
        }
        .btn-edit { background: #3498db; color: white; }
        .btn-delete { background: #e74c3c; color: white; }
        .btn-save { background: #2ecc71; color: white; }
        .back-link {
            display: inline-block;
            margin: 10px;
            text-decoration: none;
        }
    </style>
