<?php 
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';
verifier_connexion();
verifier_role('admin');

// Traitement du formulaire POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id_concours = isset($_POST['concours']) ? intval($_POST['concours']) : null;
    $page = isset($_POST['page']) ? max(1, intval($_POST['page'])) : 1;
} else {
    $id_concours = null;
    $page = 1;
}

// Pagination
$parPage = 10;
$offset = ($page - 1) * $parPage;

// Construction de la requête avec filtre
$where = '';
$params = [];
if ($id_concours) {
    $where = " WHERE cp.id_concours = :id_concours";
    $params[':id_concours'] = $id_concours;
}

// Compter total
$countQuery = "SELECT COUNT(*) FROM correction corr
               JOIN attribution a ON corr.id_attribution = a.id
               JOIN copie cp ON a.id_copie = cp.id" . $where;
$stmtCount = $pdo->prepare($countQuery);
foreach ($params as $key => $value) {
    $stmtCount->bindValue($key, $value);
}
$stmtCount->execute();
$total = $stmtCount->fetchColumn();
$nbPages = ceil($total / $parPage);

// Récupération des rapports
$query = "
    SELECT 
        cp.identifiant_anon,
        uc.nom AS candidat_nom,
        uc.prenom AS candidat_prenom,
        u.nom AS correcteur_nom,
        u.prenom AS correcteur_prenom,
        co.titre AS concours,
        corr.note_total,
        corr.commentaire_global,
        corr.date_correction
    FROM correction corr
    JOIN attribution a ON corr.id_attribution = a.id
    JOIN users u ON a.id_correcteur = u.id AND u.role = 'correcteur'
    JOIN copie cp ON a.id_copie = cp.id
    JOIN concours co ON cp.id_concours = co.id
    JOIN users uc ON cp.id_candidat = uc.id AND uc.role = 'candidat'
    $where
    ORDER BY corr.date_correction DESC
    LIMIT :offset, :parPage
";

$stmt = $pdo->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':parPage', $parPage, PDO::PARAM_INT);
$stmt->execute();
$rapports = $stmt->fetchAll();

// Récupération de la liste des concours pour le filtre
$concours = $pdo->query("SELECT id, titre FROM concours ORDER BY titre")->fetchAll();
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Rapports de correction</title>
    <link rel="stylesheet" href="../public/css/style.css">
</head>
<body>

<h2>GESTION DES RAPPORTS DE CORRECTION</h2>
<a class="back-link" href="dashboard.php">← Retour</a>

<form method="post" class="filtre-concours">
    <select name="concours">
        <option value="">Tous les concours</option>
        <?php foreach ($concours as $c): ?>
            <option value="<?= $c['id'] ?>" <?= ($id_concours == $c['id']) ? 'selected' : '' ?>>
                <?= htmlspecialchars($c['titre']) ?>
            </option>
        <?php endforeach; ?>
    </select>
    <button type="submit">Filtrer</button>
</form>

<div class="export-buttons">
    <form method="post" action="export_pdf.php" style="display: inline;">
        <?php if ($id_concours): ?>
            <input type="hidden" name="concours" value="<?= $id_concours ?>">
        <?php endif; ?>
        <button type="submit">Export PDF</button>
    </form>
    <form method="post" action="export_excel.php" style="display: inline;">
        <?php if ($id_concours): ?>
            <input type="hidden" name="concours" value="<?= $id_concours ?>">
        <?php endif; ?>
        <button type="submit">Export Excel</button>
    </form>
</div>

<table>
    <thead>
        <tr>
            <th>Identifiant anonyme</th>
            <th>Candidat</th>
            <th>Concours</th>
            <th>Correcteur</th>
            <th>Note /20</th>
            <th>Date</th>
            <th>Commentaire</th>
        </tr>
    </thead>
    <tbody>
        <?php foreach ($rapports as $r): ?>
            <tr>
                <td><?= htmlspecialchars($r['identifiant_anon']) ?></td>
                <td><?= htmlspecialchars($r['candidat_prenom'] . ' ' . $r['candidat_nom']) ?></td>
                <td><?= htmlspecialchars($r['concours']) ?></td>
                <td><?= htmlspecialchars($r['correcteur_nom'] . ' ' . $r['correcteur_prenom']) ?></td>
                <td>
                    <?php
                        $note = $r['note_total'];
                        $class = $note >= 15 ? 'note-bonne' : ($note >= 10 ? 'note-moyenne' : 'note-basse');
                        echo "<span class=\"$class\">$note /20</span>";
                    ?>
                </td>
                <td><?= htmlspecialchars($r['date_correction']) ?></td>
                <td><?= nl2br(htmlspecialchars($r['commentaire_global'])) ?></td>
            </tr>
        <?php endforeach; ?>
    </tbody>
</table>

<form method="post" class="pagination">
    <input type="hidden" name="concours" value="<?= $id_concours ?>">
    <?php for ($i = 1; $i <= $nbPages; $i++): ?>
        <?php if ($i == $page): ?>
            <span class="current"><?= $i ?></span>
        <?php else: ?>
            <button type="submit" name="page" value="<?= $i ?>"><?= $i ?></button>
        <?php endif; ?>
    <?php endfor; ?>
</form>
</body>
</html>
<style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f4f6f9;
            padding: 20px;
        }
        h2 {
            text-align: center;
            color: #2c3e50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background-color: #fff;
            margin-top: 20px;
        }
        table th, table td {
            border: 1px solid #ccc;
            padding: 10px;
            text-align: left;
        }
        table thead {
            background-color: #2980b9;
            color: white;
        }
        .note-basse {
            color: #c0392b;
            font-weight: bold;
        }
        .note-moyenne {
            color: #f39c12;
            font-weight: bold;
        }
        .note-bonne {
            color: #27ae60;
            font-weight: bold;
        }
        .pagination {
            text-align: center;
            margin-top: 15px;
        }
        .pagination button {
            margin: 0 5px;
            background: none;
            border: none;
            color: #2980b9;
            font-weight: bold;
            cursor: pointer;
        }
        .pagination span.current {
            margin: 0 5px;
            font-weight: bold;
            color: #2c3e50;
        }
        .export-buttons {
            text-align: right;
            margin-bottom: 10px;
        }
        .export-buttons button {
            padding: 6px 12px;
            margin-left: 10px;
            background-color: #16a085;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .export-buttons button:hover {
            background-color: #138d75;
        }
        .back-link {
            float: right;
            margin-top: -40px;
            background-color: #7f8c8d;
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            text-decoration: none;
        }
        .back-link:hover {
            background-color: #95a5a6;
        }
        .filtre-concours {
            margin: 20px 0;
        }
        .filtre-concours select, .filtre-concours button {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
    </style>