<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';

verifier_connexion();
verifier_role('admin');

// Traitement de la publication des résultats
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['publier_resultats'])) {
    $concours_id = intval($_POST['concours_id']);
    
    // Mettre à jour le statut de publication du concours
    $stmt = $pdo->prepare("UPDATE concours SET resultats_publies = 1, date_publication = CURRENT_TIMESTAMP WHERE id = :id");
    $stmt->execute(['id' => $concours_id]);
    
    $_SESSION['message'] = "Les résultats ont été publiés avec succès.";
    header('Location: resultats.php?concours_id=' . $concours_id);
    exit();
}

// Récupérer la liste des concours
$concours = $pdo->query("SELECT id, titre, resultats_publies FROM concours ORDER BY titre")->fetchAll();

// Récupérer les concours avec résultats publiés
$concours_publies = $pdo->query("
    SELECT id, titre, date_publication 
    FROM concours 
    WHERE resultats_publies = 1 
    ORDER BY date_publication DESC
")->fetchAll();

// Récupérer les résultats si un concours est sélectionné
$resultats = [];
$concours_selectionne = null;
if (isset($_GET['concours_id'])) {
    $concours_id = intval($_GET['concours_id']);
    
    // Récupérer les informations du concours sélectionné
    $stmt = $pdo->prepare("SELECT id, titre, resultats_publies FROM concours WHERE id = :id");
    $stmt->execute(['id' => $concours_id]);
    $concours_selectionne = $stmt->fetch();
    
    // Récupérer les résultats pour le concours sélectionné
    $stmt = $pdo->prepare("
        SELECT 
            u.nom as candidat_nom,
            u.prenom as candidat_prenom,
            m.nom as matiere_nom,
            c.note_total,
            c.commentaire_global,
            c.date_correction
        FROM copie cp
        JOIN users u ON cp.id_candidat = u.id
        JOIN matiere m ON cp.id_matiere = m.id
        LEFT JOIN attribution a ON cp.id = a.id_copie
        LEFT JOIN correction c ON a.id = c.id_attribution
        WHERE m.id_concours = :concours_id
        ORDER BY u.nom, u.prenom, m.nom
    ");
    $stmt->execute(['concours_id' => $concours_id]);
    $resultats = $stmt->fetchAll();
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Résultats des Candidats</title>
    <link rel="stylesheet" href="../public/css/style.css">
    <style>
        .resultats-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .resultats-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .resultats-table th,
        .resultats-table td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        .resultats-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .resultats-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .note {
            font-weight: bold;
        }
        .commentaire {
            max-width: 300px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .select-concours {
            margin-bottom: 20px;
        }
        .select-concours select {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
            width: 300px;
        }
        .export-btn {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 0;
        }
        .export-btn:hover {
            background-color: #45a049;
        }
        .publier-btn {
            background-color: #2196F3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        .publier-btn:hover {
            background-color: #1976D2;
        }
        .publier-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
            margin-left: 10px;
        }
        .status-publie {
            background-color: #4CAF50;
            color: white;
        }
        .status-non-publie {
            background-color: #f44336;
            color: white;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background-color: #dff0d8;
            color: #3c763d;
            border: 1px solid #d6e9c6;
        }
        .resultats-publies {
            margin-top: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .resultats-publies h3 {
            margin-top: 0;
            color: #333;
        }
        .concours-publie-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .concours-publie-item:last-child {
            border-bottom: none;
        }
        .concours-info {
            flex-grow: 1;
        }
        .concours-date {
            color: #666;
            font-size: 0.9em;
        }
        .download-btn {
            background-color: #2196F3;
            color: white;
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            margin-left: 10px;
        }
        .download-btn:hover {
            background-color: #1976D2;
        }
        .form-group {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .select-concours select {
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
            width: 300px;
            font-size: 16px;
        }
        
        .submit-btn {
            background-color: #2196F3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        .submit-btn:hover {
            background-color: #1976D2;
        }
        
        .actions-bar {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        
        .export-btn, .publier-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        .export-btn {
            background-color: #4CAF50;
            color: white;
        }
        
        .export-btn:hover {
            background-color: #45a049;
        }
        
        .publier-btn {
            background-color: #2196F3;
            color: white;
        }
        
        .publier-btn:hover {
            background-color: #1976D2;
        }
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .status-publie {
            background-color: #4CAF50;
            color: white;
        }
        
        .status-non-publie {
            background-color: #f44336;
            color: white;
        }
    </style>
</head>
<body>
    <h2>Résultats des Candidats</h2>
    <a href="dashboard.php" class="back-link">← Retour</a>

    <?php if (isset($_SESSION['message'])): ?>
        <div class="message">
            <?= htmlspecialchars($_SESSION['message']) ?>
        </div>
        <?php unset($_SESSION['message']); ?>
    <?php endif; ?>

    <div class="resultats-container">
        <form method="GET" class="select-concours">
            <div class="form-group">
                <label for="concours">Sélectionner un concours :</label>
                <select name="concours_id" id="concours">
                    <option value="">Choisir un concours...</option>
                    <?php foreach ($concours as $c): ?>
                        <option value="<?= $c['id'] ?>" <?= isset($_GET['concours_id']) && $_GET['concours_id'] == $c['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($c['titre']) ?>
                            <?= $c['resultats_publies'] ? ' (Publié)' : '' ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <button type="submit" class="submit-btn">Afficher les résultats</button>
            </div>
        </form>

        <?php if (!empty($resultats)): ?>
            <div class="actions-bar">
                <a href="export_resultats.php?concours_id=<?= $_GET['concours_id'] ?>" class="export-btn">
                    <i class="fas fa-download"></i> Exporter en PDF
                </a>
                
                <?php if ($concours_selectionne && !$concours_selectionne['resultats_publies']): ?>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="concours_id" value="<?= $concours_selectionne['id'] ?>">
                        <button type="submit" name="publier_resultats" class="publier-btn">
                            <i class="fas fa-check-circle"></i> Publier les résultats
                        </button>
                    </form>
                <?php endif; ?>
                
                <?php if ($concours_selectionne): ?>
                    <span class="status-badge <?= $concours_selectionne['resultats_publies'] ? 'status-publie' : 'status-non-publie' ?>">
                        <i class="fas <?= $concours_selectionne['resultats_publies'] ? 'fa-check-circle' : 'fa-times-circle' ?>"></i>
                        <?= $concours_selectionne['resultats_publies'] ? 'Résultats publiés' : 'Résultats non publiés' ?>
                    </span>
                <?php endif; ?>
            </div>
            
            <table class="resultats-table">
                <thead>
                    <tr>
                        <th>Candidat</th>
                        <th>Matière</th>
                        <th>Note</th>
                        <th>Date de correction</th>
                        <th>Commentaire</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($resultats as $r): ?>
                        <tr>
                            <td><?= htmlspecialchars($r['candidat_prenom'] . ' ' . $r['candidat_nom']) ?></td>
                            <td><?= htmlspecialchars($r['matiere_nom']) ?></td>
                            <td class="note"><?= $r['note_total'] ? number_format($r['note_total'], 2) : 'Non corrigé' ?></td>
                            <td><?= $r['date_correction'] ? date('d/m/Y', strtotime($r['date_correction'])) : '-' ?></td>
                            <td class="commentaire" title="<?= htmlspecialchars($r['commentaire_global'] ?? '') ?>">
                                <?= htmlspecialchars($r['commentaire_global'] ?? '') ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php elseif (isset($_GET['concours_id'])): ?>
            <p>Aucun résultat trouvé pour ce concours.</p>
        <?php endif; ?>
    </div>

    <?php if (!empty($concours_publies)): ?>
        <div class="resultats-publies">
            <h3>Résultats Publiés</h3>
            <?php foreach ($concours_publies as $cp): ?>
                <div class="concours-publie-item">
                    <div class="concours-info">
                        <strong><?= htmlspecialchars($cp['titre']) ?></strong>
                        <div class="concours-date">
                            Publié le <?= date('d/m/Y', strtotime($cp['date_publication'])) ?>
                        </div>
                    </div>
                    <a href="export_resultats.php?concours_id=<?= $cp['id'] ?>" class="download-btn">
                        Télécharger les résultats
                    </a>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</body>
</html> 