<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';
verifier_connexion();
verifier_role('admin');
require_once '../includes/header.php';
if (isset($_POST['changer_role'], $_POST['id'], $_POST['nouveau_role'])) {
    $stmt = $pdo->prepare("UPDATE users SET role = ? WHERE id = ?");
    $stmt->execute([$_POST['nouveau_role'], $_POST['id']]);
    header("Location: users.php");
    exit;
}

if (isset($_GET['desactiver']) && is_numeric($_GET['desactiver'])) {
    $stmt = $pdo->prepare("UPDATE users SET actif = 0 WHERE id = ?");
    $stmt->execute([$_GET['desactiver']]);
    header("Location: users.php");
    exit;
}

$where = [];
$params = [];

if (!empty($_GET['filtre_role'])) {
    $where[] = "role = ?";
    $params[] = $_GET['filtre_role'];
}

if (!empty($_GET['date_min'])) {
    $where[] = "DATE(date_inscription) >= ?";
    $params[] = $_GET['date_min'];
}
if (!empty($_GET['date_max'])) {
    $where[] = "DATE(date_inscription) <= ?";
    $params[] = $_GET['date_max'];
}

$sql = "SELECT * FROM users";
if ($where) {
    $sql .= " WHERE " . implode(" AND ", $where);
}
$sql .= " ORDER BY role, nom";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$utilisateurs = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html>
<head>
    <title>Gestion des utilisateurs</title>
    <link rel="stylesheet" href="../public/css/style.css">
</head>
<body>

    <div class="header-container">
        <h2>GESTION DES UTILISATEURS</h2>
        <a class="back-link" href="dashboard.php">Retour</a>
    </div>

    <form method="GET">
        <label>Rôle :
            <select name="filtre_role">
                <option value="">Tous</option>
                <option value="admin">Admin</option>
                <option value="candidat">Candidat</option>
                <option value="correcteur">Correcteur</option>
            </select>
        </label>
        <label>Date min :
            <input type="date" name="date_min">
        </label>
        <label>Date max :
            <input type="date" name="date_max">
        </label>
        <button type="submit">Filtrer</button>
    </form>

    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Nom</th>
                <th>Prénom</th>
                <th>Email</th>
                <th>Rôle</th>
                <th>Actif</th>
                <th>Date</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($utilisateurs as $u): ?>
                <tr>
                    <td><?= $u['id'] ?></td>
                    <td><?= htmlspecialchars($u['nom']) ?></td>
                    <td><?= htmlspecialchars($u['prenom']) ?></td>
                    <td><?= htmlspecialchars($u['email']) ?></td>
                    <td><?= $u['role'] ?></td>
                    <td><?= $u['actif'] ? 'Oui' : 'Non' ?></td>
                    <td><?= $u['date_inscription'] ?></td>
                    <td>
                        <form method="POST" style="display:inline">
                            <input type="hidden" name="id" value="<?= $u['id'] ?>">
                            <select name="nouveau_role">
                                <option value="admin" <?= $u['role'] == 'admin' ? 'selected' : '' ?>>admin</option>
                                <option value="candidat" <?= $u['role'] == 'candidat' ? 'selected' : '' ?>>candidat</option>
                                <option value="correcteur" <?= $u['role'] == 'correcteur' ? 'selected' : '' ?>>correcteur</option>
                            </select>
                            <button type="submit" name="changer_role">OK</button>
                        </form>
                        <?php if ($u['actif']): ?>
                            <a href="?desactiver=<?= $u['id'] ?>" onclick="return confirm('Désactiver cet utilisateur ?')">Désactiver</a>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

</body>
</html>
<?php require_once '../includes/footer.php'; ?>
<style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f5f6fa;
            margin: 0;
            padding: 20px;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 0 10%;
        }

        h2 {
            color: #2c3e50;
            font-size: 24px;
        }

        .back-link {
            background-color: #2c3e50;
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 14px;
            font-weight: bold;
        }

        .back-link:hover {
            background-color: #2980b9;
            color: #fff;
        }

        form {
            margin: 20px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            width: 80%;
            margin-left: auto;
            margin-right: auto;
        }

        form label {
            font-weight: bold;
            color: #34495e;
        }

        form select, form input[type="date"] {
            padding: 5px;
            border-radius: 4px;
            border: 1px solid #ccc;
        }

        form button {
            background-color: #2980b9;
            color: white;
            padding: 6px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        table {
            width: 80%;
            margin: 0 auto;
            border-collapse: collapse;
            background-color: white;
            border: 1px solid #ddd;
            max-height: 400px;
            overflow-y: auto;
            display: block;
        }

        table thead {
            background-color: #3498db;
            color: white;
        }

        table th, table td {
            padding: 6px 10px;
            text-align: left;
            border-bottom: 1px solid #eee;
            font-size: 13px;
        }

        tr:hover {
            background-color: #f2f2f2;
        }

        form[method="POST"] select {
            padding: 4px;
        }

        form[method="POST"] button {
            padding: 4px 10px;
            background-color: #27ae60;
        }

        a {
            color: #c0392b;
            text-decoration: none;
            font-weight: bold;
        }

        a:hover {
            text-decoration: underline;
        }
    </style>
