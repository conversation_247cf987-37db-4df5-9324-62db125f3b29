<?php 
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';
verifier_connexion();
verifier_role('candidat');

// Définir le titre de la page avant d'inclure le header
$page_title = "Tableau de bord candidat";
require_once '../includes/header.php';

$id_candidat = $_SESSION['user']['id'];

$nb_copies = $pdo->prepare("SELECT COUNT(*) FROM copie WHERE id_candidat = ?");
$nb_copies->execute([$id_candidat]);
$total = $nb_copies->fetchColumn();

$copie_corrigee = $pdo->prepare("SELECT COUNT(*) FROM copie WHERE id_candidat = ? AND statut = 'corrigee'");
$copie_corrigee->execute([$id_candidat]);
$corrigees = $copie_corrigee->fetchColumn();
?>

<h2>Bienvenue <?= htmlspecialchars($_SESSION['user']['prenom']) ?></h2>

<div class="stats-container">
    <div class="stat-box">
        <div class="stat-icon">📄</div>
        <div class="stat-number" id="totalCopies"><?= $total ?></div>
        <div class="stat-label">Copies déposées</div>
    </div>
    <div class="stat-box">
        <div class="stat-icon">✅</div>
        <div class="stat-number" id="copiesCorrigees"><?= $corrigees ?></div>
        <div class="stat-label">Copies corrigées</div>
    </div>
</div>

<ul class="dashboard-links">
    <li><a href="inscription_concours.php">S'inscrire à un concours</a></li>
    <li><a href="depot.php">Déposer une copie</a></li>
    <li><a href="mes_copies.php">Mes copies</a></li>
    <li><a href="resultats.php">Voir mes résultats</a></li>
    <li><a href="profil.php">Modifier mon profil</a></li>
</ul>

<script>
    // Petite animation de compteur
    function animateCount(id, endValue) {
        let el = document.getElementById(id);
        let current = 0;
        let duration = 800;
        let increment = Math.ceil(endValue / (duration / 50));

        let interval = setInterval(() => {
            current += increment;
            if (current >= endValue) {
                current = endValue;
                clearInterval(interval);
            }
            el.innerText = current;
        }, 50);
    }

    animateCount("totalCopies", <?= $total ?>);
    animateCount("copiesCorrigees", <?= $corrigees ?>);
</script>

<style>
    .stats-container {
        display: flex;
        justify-content: center;
        gap: 30px;
        margin: 30px auto;
        flex-wrap: wrap;
    }

    .stat-box {
        background-color: #ffffff;
        padding: 25px 30px;
        border-radius: 10px;
        width: 250px;
        text-align: center;
        box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        transition: transform 0.3s ease;
    }

    .stat-box:hover {
        transform: translateY(-5px);
    }

    .stat-icon {
        font-size: 2.5em;
        color: #3498db;
        margin-bottom: 10px;
    }

    .stat-number {
        font-size: 2em;
        font-weight: bold;
        color: #2c3e50;
    }

    .stat-label {
        font-size: 1em;
        color: #555;
    }

    .dashboard-links {
        list-style: none;
        padding: 0;
        margin-top: 40px;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 15px;
    }

    .dashboard-links li a {
        display: inline-block;
        padding: 12px 20px;
        background-color: #3498db;
        color: white;
        text-decoration: none;
        border-radius: 6px;
        font-weight: bold;
        transition: background-color 0.3s ease;
    }

    .dashboard-links li a:hover {
        background-color: #2980b9;
    }

    @media (max-width: 600px) {
        .stats-container {
            flex-direction: column;
            align-items: center;
        }

        .dashboard-links li a {
            width: 80%;
            text-align: center;
        }
    }
</style>

<?php require_once '../includes/footer.php'; ?>
