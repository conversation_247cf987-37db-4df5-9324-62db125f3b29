<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';
verifier_connexion();
verifier_role('candidat');

// Définir le titre de la page avant d'inclure le header
$page_title = "Dépôt de copie";
require_once '../includes/header.php';

$id_candidat = utilisateur()['id'];
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['fichier'])) {
    $id_concours = $_POST['id_concours'] ?? null;
    $id_matiere = $_POST['id_matiere'] ?? null;
    $fichier = $_FILES['fichier'];

    if ($id_concours && $id_matiere && $fichier['error'] === 0) {
        // Vérifier si le candidat est inscrit au concours
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM inscription_concours WHERE id_candidat = ? AND id_concours = ?");
        $stmt->execute([$id_candidat, $id_concours]);
        $est_inscrit = $stmt->fetchColumn();

        // Vérifier si le concours est toujours actif
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM concours WHERE id = ? AND date_fin >= CURRENT_DATE");
        $stmt->execute([$id_concours]);
        $est_actif = $stmt->fetchColumn();

        // Vérifier si la matière appartient bien au concours
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM matiere WHERE id = ? AND id_concours = ?");
        $stmt->execute([$id_matiere, $id_concours]);
        $matiere_valide = $stmt->fetchColumn();

        if (!$est_inscrit) {
            $error = "Vous n'êtes pas inscrit à ce concours.";
        } elseif (!$est_actif) {
            $error = "Ce concours est terminé, vous ne pouvez plus déposer de copie.";
        } elseif (!$matiere_valide) {
            $error = "La matière sélectionnée n'est pas valide pour ce concours.";
        } elseif ($fichier['size'] > 10 * 1024 * 1024) { // Limite de 10 Mo
            $error = "Le fichier est trop volumineux. Taille maximale : 10 Mo.";
        } else {
            $ext = pathinfo($fichier['name'], PATHINFO_EXTENSION);
            if (in_array(strtolower($ext), ['pdf', 'zip'])) {
                $identifiant = uniqid('copy_');
                $nom_fichier = $identifiant . '.' . $ext;
                $chemin = '../public/uploads/' . $nom_fichier;

                if (move_uploaded_file($fichier['tmp_name'], $chemin)) {
                    $stmt = $pdo->prepare("INSERT INTO copie (id_candidat, id_concours, id_matiere, identifiant_anon, fichier_path) VALUES (?, ?, ?, ?, ?)");
                    $stmt->execute([$id_candidat, $id_concours, $id_matiere, $identifiant, $nom_fichier]);
                    $success = "Copie déposée avec succès.";
                } else {
                    $error = "Erreur lors de l'upload du fichier.";
                }
            } else {
                $error = "Format non autorisé. (PDF ou ZIP uniquement)";
            }
        }
    } else {
        $error = "Tous les champs sont requis.";
    }
}

// Récupérer uniquement les concours actifs auxquels le candidat est inscrit
$concours = $pdo->prepare("
    SELECT c.* 
    FROM concours c
    JOIN inscription_concours ic ON c.id = ic.id_concours
    WHERE ic.id_candidat = ? 
    AND c.date_fin >= CURRENT_DATE
    ORDER BY c.date_fin ASC
");
$concours->execute([$id_candidat]);
$concours = $concours->fetchAll();

// Récupérer toutes les matières pour les concours actifs
$matieres = $pdo->prepare("
    SELECT m.* 
    FROM matiere m
    JOIN concours c ON m.id_concours = c.id
    JOIN inscription_concours ic ON c.id = ic.id_concours
    WHERE ic.id_candidat = ? 
    AND c.date_fin >= CURRENT_DATE
    ORDER BY c.titre, m.nom
");
$matieres->execute([$id_candidat]);
$matieres_liste = $matieres->fetchAll();

// Organiser les matières par concours
$matieres_par_concours = [];
foreach ($matieres_liste as $matiere) {
    if (!isset($matieres_par_concours[$matiere['id_concours']])) {
        $matieres_par_concours[$matiere['id_concours']] = [];
    }
    $matieres_par_concours[$matiere['id_concours']][] = $matiere;
}
?>

<h2>Dépôt de copie</h2>

<?php if ($success): ?>
    <div class="message success"><?= htmlspecialchars($success) ?></div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="message error"><?= htmlspecialchars($error) ?></div>
<?php endif; ?>

<?php if (empty($concours)): ?>
    <div class="message info">
        Vous n'êtes inscrit à aucun concours actif. 
        <a href="inscription_concours.php">Inscrivez-vous à un concours</a> pour pouvoir déposer une copie.
    </div>
<?php else: ?>
    <form method="POST" enctype="multipart/form-data" class="depot-form">
        <label for="id_concours">Choisir un concours :</label>
        <select name="id_concours" id="id_concours" required onchange="updateMatieres()">
            <option value="">-- Sélectionnez --</option>
            <?php foreach ($concours as $c): ?>
                <option value="<?= $c['id'] ?>"><?= htmlspecialchars($c['titre']) ?></option>
            <?php endforeach; ?>
        </select>

        <label for="id_matiere">Choisir une matière :</label>
        <select name="id_matiere" id="id_matiere" required>
            <option value="">-- Sélectionnez d'abord un concours --</option>
        </select>

        <label for="fichier">Fichier (PDF ou ZIP, max 10 Mo) :</label>
        <input type="file" name="fichier" id="fichier" required>

        <button type="submit">Déposer</button>
    </form>

    <script>
        // Données des matières par concours
        const matieresConcours = <?= json_encode($matieres_par_concours) ?>;
        
        // Fonction pour mettre à jour les options de matières en fonction du concours sélectionné
        function updateMatieres() {
            const concoursSelect = document.getElementById('id_concours');
            const matiereSelect = document.getElementById('id_matiere');
            
            // Vider le select des matières
            matiereSelect.innerHTML = '';
            
            // Si aucun concours n'est sélectionné
            if (!concoursSelect.value) {
                const option = document.createElement('option');
                option.value = '';
                option.textContent = '-- Sélectionnez d\'abord un concours --';
                matiereSelect.appendChild(option);
                return;
            }
            
            // Récupérer les matières du concours sélectionné
            const matieres = matieresConcours[concoursSelect.value] || [];
            
            // Si aucune matière n'est disponible
            if (matieres.length === 0) {
                const option = document.createElement('option');
                option.value = '';
                option.textContent = 'Aucune matière disponible pour ce concours';
                matiereSelect.appendChild(option);
                return;
            }
            
            // Ajouter l'option par défaut
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = '-- Sélectionnez une matière --';
            matiereSelect.appendChild(defaultOption);
            
            // Ajouter les options de matières
            matieres.forEach(matiere => {
                const option = document.createElement('option');
                option.value = matiere.id;
                option.textContent = matiere.nom + ' (coefficient: ' + matiere.coefficient + ')';
                matiereSelect.appendChild(option);
            });
        }
        
        // Initialiser les matières au chargement de la page
        document.addEventListener('DOMContentLoaded', updateMatieres);
    </script>
<?php endif; ?>

<style>
    .message {
        padding: 12px;
        margin-bottom: 20px;
        border-radius: 5px;
        text-align: center;
    }

    .message.success {
        background-color: #d4edda;
        color: #155724;
    }

    .message.error {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .message.info {
        background-color: #d1ecf1;
        color: #0c5460;
    }

    .depot-form {
        background: #fff;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        max-width: 500px;
        margin: 0 auto;
    }

    .depot-form label {
        display: block;
        font-weight: bold;
        margin-bottom: 8px;
    }

    .depot-form select, 
    .depot-form input[type="file"] {
        width: 100%;
        padding: 10px;
        margin-bottom: 20px;
        border: 1px solid #ccc;
        border-radius: 6px;
    }

    .depot-form button {
        background: #28a745;
        color: #fff;
        padding: 12px 20px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 16px;
        width: 100%;
        transition: background 0.3s ease;
    }

    .depot-form button:hover {
        background: #218838;
    }

    @media (max-width: 600px) {
        .depot-form {
            padding: 20px;
        }

        .depot-form button {
            font-size: 14px;
        }
    }
</style>

<?php require_once '../includes/footer.php'; ?>
