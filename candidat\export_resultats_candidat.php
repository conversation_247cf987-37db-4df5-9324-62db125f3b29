<?php
require_once __DIR__ . '/../vendor/tecnickcom/tcpdf/tcpdf.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';

verifier_connexion();
verifier_role('candidat');

$id_candidat = utilisateur()['id'];

// Récupérer les informations du candidat
$stmt_candidat = $pdo->prepare("SELECT nom, prenom FROM users WHERE id = ?");
$stmt_candidat->execute([$id_candidat]);
$candidat_info = $stmt_candidat->fetch();

if (!$candidat_info) {
    die('Informations candidat non trouvées');
}

// Récupération des résultats par matière pour le candidat
$resultats = $pdo->prepare("
    SELECT 
        c.titre as concours_nom,
        m.nom as matiere_nom,
        m.coefficient,
        corr.note_total,
        corr.commentaire_global
    FROM copie cp
    JOIN concours c ON cp.id_concours = c.id
    JOIN matiere m ON cp.id_matiere = m.id
    JOIN attribution a ON cp.id = a.id_copie
    JOIN correction corr ON a.id = corr.id_attribution
    WHERE cp.id_candidat = ?
    ORDER BY c.titre, m.nom
");
$resultats->execute([$id_candidat]);
$resultats_liste = $resultats->fetchAll();

// Calcul des moyennes par concours pour le candidat
$moyennes = [];
foreach ($resultats_liste as $r) {
    if (!isset($moyennes[$r['concours_nom']])) {
        $moyennes[$r['concours_nom']] = [
            'total_notes' => 0,
            'total_coefficients' => 0,
            'resultats' => [] // Pour stocker les résultats de chaque matière
        ];
    }
    $moyennes[$r['concours_nom']]['total_notes'] += $r['note_total'] * $r['coefficient'];
    $moyennes[$r['concours_nom']]['total_coefficients'] += $r['coefficient'];
    $moyennes[$r['concours_nom']]['resultats'][] = $r;
}


// Créer le PDF
$pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

// Définir les informations du document
$pdf->SetCreator(PDF_CREATOR);
$pdf->SetAuthor('Système de Gestion des Concours');
$pdf->SetTitle('Fiche de Résultats - ' . $candidat_info['prenom'] . ' ' . $candidat_info['nom']);

// Définir les marges
$pdf->SetMargins(15, 15, 15);
$pdf->SetHeaderMargin(5);
$pdf->SetFooterMargin(10);

// Ajouter une page
$pdf->AddPage();

// Informations du candidat
$pdf->SetFont('helvetica', 'B', 14);
$pdf->Cell(0, 10, 'Fiche de Résultats', 0, 1, 'C');
$pdf->SetFont('helvetica', '', 12);
$pdf->Cell(0, 7, 'Candidat: ' . htmlspecialchars($candidat_info['prenom'] . ' ' . $candidat_info['nom']), 0, 1, 'L');
$pdf->Ln(5);

foreach ($moyennes as $concours_nom => $data) {
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->Cell(0, 10, 'Concours: ' . htmlspecialchars($concours_nom), 0, 1, 'L');
    $pdf->SetFont('helvetica', '', 10);
    $pdf->Cell(0, 7, 'Moyenne Générale: ' . number_format($data['total_notes'] / $data['total_coefficients'], 2) . '/20', 0, 1, 'L');
    $pdf->Ln(5);

    // En-têtes du tableau
    $pdf->SetFont('helvetica', 'B', 9);
    $header_widths = [40, 20, 20, 100]; // Matière, Coefficient, Note, Commentaire (Adjusted for removed Correcteur)
    
    $pdf->Cell($header_widths[0], 8, 'Matière', 1, 0, 'C');
    $pdf->Cell($header_widths[1], 8, 'Coeff.', 1, 0, 'C');
    $pdf->Cell($header_widths[2], 8, 'Note', 1, 0, 'C');
    $pdf->Cell($header_widths[3], 8, 'Commentaire', 1, 1, 'C');

    // Données du tableau
    $pdf->SetFont('helvetica', '', 8);
    foreach ($data['resultats'] as $r) {
        $matiere_text = htmlspecialchars($r['matiere_nom']);
        $note_text = $r['note_total'] ? number_format($r['note_total'], 2) : 'Non corrigé';
        $commentaire_text = htmlspecialchars($r['commentaire_global'] ?? '');

        $current_x = $pdf->GetX();
        $current_y = $pdf->GetY();

        // Calculate required height for the 'Commentaire' MultiCell using a transaction
        $pdf->startTransaction();
        $pdf->MultiCell($header_widths[3], 0, $commentaire_text, 0, 'L', 0, 1, '', '', true, 1, false, true);
        $comment_height_needed = $pdf->GetY() - $current_y;
        $pdf->rollbackTransaction(true);

        $row_height = max(8, $comment_height_needed); // Use 8mm as minimum height for a row

        // Check for page break BEFORE drawing the row
        if (($pdf->GetY() + $row_height) > ($pdf->getPageHeight() - $pdf->getBreakMargin())) {
            $pdf->AddPage();
            // Redraw candidate info on new page if necessary
            $pdf->SetFont('helvetica', 'B', 14);
            $pdf->Cell(0, 10, 'Fiche de Résultats', 0, 1, 'C');
            $pdf->SetFont('helvetica', '', 12);
            $pdf->Cell(0, 7, 'Candidat: ' . htmlspecialchars($candidat_info['prenom'] . ' ' . $candidat_info['nom']), 0, 1, 'L');
            $pdf->Ln(5);
            // Redraw concours info
            $pdf->SetFont('helvetica', 'B', 12);
            $pdf->Cell(0, 10, 'Concours: ' . htmlspecialchars($concours_nom), 0, 1, 'L');
            $pdf->SetFont('helvetica', '', 10);
            $pdf->Cell(0, 7, 'Moyenne Générale: ' . number_format($data['total_notes'] / $data['total_coefficients'], 2) . '/20', 0, 1, 'L');
            $pdf->Ln(5);

            // Redraw headers on new page
            $pdf->SetFont('helvetica', 'B', 9);
            $pdf->Cell($header_widths[0], 8, 'Matière', 1, 0, 'C');
            $pdf->Cell($header_widths[1], 8, 'Coeff.', 1, 0, 'C');
            $pdf->Cell($header_widths[2], 8, 'Note', 1, 0, 'C');
            $pdf->Cell($header_widths[3], 8, 'Commentaire', 1, 1, 'C');
            $pdf->SetFont('helvetica', '', 8); // Reset font for data
        }

        // Print cells for the current row
        $x_offset = $pdf->GetX();
        $current_y = $pdf->GetY();

        // Matière
        $pdf->SetXY($x_offset, $current_y);
        $pdf->Cell($header_widths[0], $row_height, $matiere_text, 1, 0, 'L', 0, '', 0, true);
        $x_offset += $header_widths[0];

        // Coefficient
        $pdf->SetXY($x_offset, $current_y);
        $pdf->Cell($header_widths[1], $row_height, $r['coefficient'], 1, 0, 'C', 0, '', 0, true);
        $x_offset += $header_widths[1];

        // Note
        $pdf->SetXY($x_offset, $current_y);
        $pdf->Cell($header_widths[2], $row_height, $note_text, 1, 0, 'C', 0, '', 0, true);
        $x_offset += $header_widths[2];
        
        // Commentaire (MultiCell)
        $pdf->SetXY($x_offset, $current_y);
        $pdf->MultiCell($header_widths[3], $row_height, $commentaire_text, 1, 'L', 0, 1, '', '', true, 1, false, true, $row_height, 'T');
    }
    $pdf->Ln(10); // Add some space after each concours section
}


// Générer le PDF
$pdf->Output('fiche_resultats_' . $candidat_info['nom'] . '.pdf', 'I'); 