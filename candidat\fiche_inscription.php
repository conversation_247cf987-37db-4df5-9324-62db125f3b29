<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';

verifier_connexion();
verifier_role('candidat');

$id_candidat = $_SESSION['user']['id'];

// Récupérer les informations du candidat
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$id_candidat]);
$candidat = $stmt->fetch();

// Récupérer les concours auxquels le candidat est inscrit
$stmt = $pdo->prepare("
    SELECT c.*, ic.date_inscription, 
           (SELECT COUNT(*) FROM copie WHERE id_concours = c.id AND id_candidat = ?) as copies_deposees
    FROM concours c
    JOIN inscription_concours ic ON c.id = ic.id_concours
    WHERE ic.id_candidat = ?
    ORDER BY c.date_debut DESC
");
$stmt->execute([$id_candidat, $id_candidat]);
$concours_inscrits = $stmt->fetchAll();

// Récupérer les matières pour chaque concours
$matieres_par_concours = [];
$copies_par_matiere = [];

foreach ($concours_inscrits as $concours) {
    // Récupérer les matières du concours
    $stmt = $pdo->prepare("
        SELECT m.* 
        FROM matiere m
        WHERE m.id_concours = ?
        ORDER BY m.nom
    ");
    $stmt->execute([$concours['id']]);
    $matieres = $stmt->fetchAll();
    $matieres_par_concours[$concours['id']] = $matieres;
    
    // Récupérer les copies déjà déposées par matière
    $stmt = $pdo->prepare("
        SELECT id_matiere, COUNT(*) as nb_copies
        FROM copie
        WHERE id_concours = ? AND id_candidat = ?
        GROUP BY id_matiere
    ");
    $stmt->execute([$concours['id'], $id_candidat]);
    $copies = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    $copies_par_matiere[$concours['id']] = $copies;
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Ma fiche d'inscription</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    
</head>
<body>
    <div class="container">
        <h1>Ma fiche d'inscription</h1>

        <div class="actions">
            <a href="generer_pdf.php" class="btn-pdf">
                <i class="fas fa-file-pdf"></i> Télécharger en PDF
            </a>
        </div>

        <div class="section">
            <h2>Informations personnelles</h2>
            <div class="info-group">
                <span class="info-label">Nom :</span>
                <?= htmlspecialchars($candidat['nom']) ?>
            </div>
            <div class="info-group">
                <span class="info-label">Prénom :</span>
                <?= htmlspecialchars($candidat['prenom']) ?>
            </div>
            <div class="info-group">
                <span class="info-label">Email :</span>
                <?= htmlspecialchars($candidat['email']) ?>
            </div>
        </div>

        <div class="section">
            <h2>Mes concours</h2>
            <?php if (empty($concours_inscrits)): ?>
                <p>Vous n'êtes inscrit à aucun concours pour le moment.</p>
            <?php else: ?>
                <div class="concours-grid">
                    <?php foreach ($concours_inscrits as $concours): ?>
                        <div class="concours-card">
                            <h3><?= htmlspecialchars($concours['titre']) ?></h3>
                            <div class="info-group">
                                <span class="info-label">Date d'inscription :</span>
                                <?= date('d/m/Y H:i', strtotime($concours['date_inscription'])) ?>
                            </div>
                            <div class="info-group">
                                <span class="info-label">Date de début :</span>
                                <?= date('d/m/Y', strtotime($concours['date_debut'])) ?>
                            </div>
                            <div class="info-group">
                                <span class="info-label">Date de fin :</span>
                                <?= date('d/m/Y', strtotime($concours['date_fin'])) ?>
                            </div>
                            <div class="info-group">
                                <span class="info-label">Copies déposées :</span>
                                <?= $concours['copies_deposees'] ?>
                            </div>
                            
                            <!-- Affichage des matières du concours -->
                            <?php if (!empty($matieres_par_concours[$concours['id']])): ?>
                                <div class="matieres-section">
                                    <h4>Matières à préparer :</h4>
                                    <ul class="matieres-list">
                                        <?php foreach ($matieres_par_concours[$concours['id']] as $matiere): ?>
                                            <li class="matiere-item">
                                                <?= htmlspecialchars($matiere['nom']) ?> 
                                                <span class="coefficient">(coef. <?= $matiere['coefficient'] ?>)</span>
                                                <?php if (isset($copies_par_matiere[$concours['id']][$matiere['id']]) && $copies_par_matiere[$concours['id']][$matiere['id']] > 0): ?>
                                                    <span class="badge submitted">
                                                        <i class="fas fa-check"></i> Copie déposée
                                                    </span>
                                                <?php elseif ($concours['date_fin'] >= date('Y-m-d')): ?>
                                                    <span class="badge pending">
                                                        <i class="fas fa-clock"></i> En attente
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge missed">
                                                        <i class="fas fa-times"></i> Non déposée
                                                    </span>
                                                <?php endif; ?>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                    
                                    <?php if ($concours['date_fin'] >= date('Y-m-d')): ?>
                                        <a href="depot.php" class="btn-depot">
                                            <i class="fas fa-upload"></i> Déposer une copie
                                        </a>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($concours['date_fin'] >= date('Y-m-d')): ?>
                                <span class="status active">Concours en cours</span>
                            <?php else: ?>
                                <span class="status completed">Concours terminé</span>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <a href="dashboard.php" class="back-link">
            <i class="fas fa-arrow-left"></i> Retour au tableau de bord
        </a>
    </div>
</body>
</html> 
<style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f4f6f9;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .info-group {
            margin-bottom: 15px;
        }

        .info-label {
            font-weight: bold;
            color: #34495e;
        }

        .concours-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .concours-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .concours-card h3 {
            color: #2c3e50;
            margin-top: 0;
        }

        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            margin-top: 10px;
        }

        .status.active {
            background-color: #2ecc71;
            color: white;
        }

        .status.completed {
            background-color: #3498db;
            color: white;
        }

        .back-link {
            display: inline-block;
            margin-top: 20px;
            color: #3498db;
            text-decoration: none;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        .actions {
            text-align: right;
            margin-bottom: 20px;
        }

        .btn-pdf {
            display: inline-block;
            padding: 10px 20px;
            background-color: #e74c3c;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background-color 0.3s;
        }

        .btn-pdf:hover {
            background-color: #c0392b;
        }

        .btn-pdf i {
            margin-right: 5px;
        }

        /* Styles pour la section des matières */
        .matieres-section {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        .matieres-section h4 {
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .matieres-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .matiere-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .matiere-item:last-child {
            border-bottom: none;
        }

        .coefficient {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-left: 5px;
        }

        .badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-left: 10px;
            white-space: nowrap;
        }

        .badge.submitted {
            background-color: #2ecc71;
            color: white;
        }

        .badge.pending {
            background-color: #f39c12;
            color: white;
        }

        .badge.missed {
            background-color: #e74c3c;
            color: white;
        }

        .btn-depot {
            display: inline-block;
            margin-top: 15px;
            padding: 8px 15px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .btn-depot:hover {
            background-color: #2980b9;
        }

        .btn-depot i {
            margin-right: 5px;
        }
    </style>