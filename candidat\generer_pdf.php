<?php
/**
 * Générateur de PDF pour les fiches d'inscription des candidats
 *
 * Ce fichier génère un PDF contenant les informations d'inscription
 * d'un candidat aux concours auxquels il est inscrit.
 *
 * <AUTHOR> de gestion des concours
 * @version 1.0
 *
 * @noinspection PhpUndefinedMethodInspection
 * @noinspection PhpUndefinedClassInspection
 */

require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';

// Vérifier si TCPDF est disponible
$tcpdf_path = __DIR__ . '/../vendor/tecnickcom/tcpdf/tcpdf.php';
if (!file_exists($tcpdf_path)) {
    die("Erreur : La bibliothèque TCPDF n'est pas installée. Contactez l'administrateur.");
}
require_once $tcpdf_path;

verifier_connexion();
verifier_role('candidat');

$id_candidat = $_SESSION['user']['id'];

// Récupérer les informations du candidat
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$id_candidat]);
$candidat = $stmt->fetch();

if (!$candidat) {
    die("Erreur : Candidat non trouvé.");
}

// Récupérer les concours auxquels le candidat est inscrit
$stmt = $pdo->prepare("
    SELECT c.*, ic.date_inscription, 
           (SELECT COUNT(*) FROM copie WHERE id_concours = c.id AND id_candidat = ?) as copies_deposees
    FROM concours c
    JOIN inscription_concours ic ON c.id = ic.id_concours
    WHERE ic.id_candidat = ?
    ORDER BY c.date_debut DESC
");
$stmt->execute([$id_candidat, $id_candidat]);
$concours_inscrits = $stmt->fetchAll();

// Récupérer les matières pour chaque concours
$matieres_par_concours = [];
$copies_par_matiere = [];

foreach ($concours_inscrits as $concours) {
    // Récupérer les matières du concours
    $stmt = $pdo->prepare("
        SELECT m.* 
        FROM matiere m
        WHERE m.id_concours = ?
        ORDER BY m.nom
    ");
    $stmt->execute([$concours['id']]);
    $matieres = $stmt->fetchAll();
    $matieres_par_concours[$concours['id']] = $matieres;
    
    // Récupérer les copies déjà déposées par matière
    $stmt = $pdo->prepare("
        SELECT id_matiere, COUNT(*) as nb_copies
        FROM copie
        WHERE id_concours = ? AND id_candidat = ?
        GROUP BY id_matiere
    ");
    $stmt->execute([$concours['id'], $id_candidat]);
    $copies = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    $copies_par_matiere[$concours['id']] = $copies;
}

/**
 * Classe personnalisée pour la génération de PDF
 * Étend TCPDF pour ajouter un header et footer personnalisés
 *
 * @extends TCPDF
 */
class MYPDF extends TCPDF {
    public function __construct($orientation = 'P', $unit = 'mm', $format = 'A4', $unicode = true, $encoding = 'UTF-8', $diskcache = false) {
        parent::__construct($orientation, $unit, $format, $unicode, $encoding, $diskcache);

        // Configuration automatique
        $this->SetAutoPageBreak(TRUE, 20);
        $this->setImageScale(1.25);
    }

    public function Header() {
        // Logo ou titre
        $this->SetFont('helvetica', 'B', 18);
        $this->SetTextColor(0, 0, 0);
        $this->Cell(0, 15, "Fiche d'inscription aux concours", 0, false, 'C');
        $this->Ln(8);

        // Ligne de séparation
        $this->SetDrawColor(200, 200, 200);
        $this->Line(15, $this->GetY(), 195, $this->GetY());
        $this->Ln(5);
    }

    public function Footer() {
        $this->SetY(-15);
        $this->SetFont('helvetica', 'I', 8);
        $this->SetTextColor(128, 128, 128);
        $this->Cell(0, 10, 'Page ' . $this->getAliasNumPage() . ' / ' . $this->getAliasNbPages(), 0, false, 'C');
    }
}

try {
    // Créer l'instance PDF (suppression des avertissements IDE pour les méthodes TCPDF)
    /** @noinspection PhpUndefinedMethodInspection */
    $pdf = new MYPDF('P', 'mm', 'A4', true, 'UTF-8', false);

    // Définir les informations du document
    $pdf->SetCreator('TCPDF');
    $pdf->SetAuthor('Système de gestion des concours');
    $pdf->SetTitle("Fiche d'inscription - " . htmlspecialchars($candidat['nom']) . ' ' . htmlspecialchars($candidat['prenom']));
    $pdf->SetSubject('Fiche d\'inscription aux concours');
    $pdf->SetKeywords('concours, inscription, candidat');
} catch (Exception $e) {
    die("Erreur lors de la création du PDF : " . $e->getMessage());
}

// Définir les marges
$pdf->SetMargins(15, 40, 15);
$pdf->SetHeaderMargin(20);
$pdf->SetFooterMargin(10);

// Ajouter une page
$pdf->AddPage();

// Informations personnelles
$pdf->SetFont('helvetica', 'B', 14);
$pdf->Cell(0, 10, 'Informations personnelles', 0, 1, 'L');
$pdf->SetFont('helvetica', '', 12);

$pdf->Cell(40, 7, 'Nom :', 0, 0);
$pdf->Cell(0, 7, $candidat['nom'], 0, 1);

$pdf->Cell(40, 7, 'Prénom :', 0, 0);
$pdf->Cell(0, 7, $candidat['prenom'], 0, 1);

$pdf->Cell(40, 7, 'Email :', 0, 0);
$pdf->Cell(0, 7, $candidat['email'], 0, 1);

$pdf->Ln(10);

// Concours
$pdf->SetFont('helvetica', 'B', 14);
$pdf->Cell(0, 10, 'Concours inscrits', 0, 1, 'L');
$pdf->SetFont('helvetica', '', 12);

if (empty($concours_inscrits)) {
    $pdf->Cell(0, 7, 'Aucun concours inscrit', 0, 1);
} else {
    foreach ($concours_inscrits as $concours) {
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 7, $concours['titre'], 0, 1);
        $pdf->SetFont('helvetica', '', 10);
        
        $pdf->Cell(50, 6, 'Date d\'inscription :', 0, 0);
        $pdf->Cell(0, 6, date('d/m/Y H:i', strtotime($concours['date_inscription'])), 0, 1);
        
        $pdf->Cell(50, 6, 'Date de début :', 0, 0);
        $pdf->Cell(0, 6, date('d/m/Y', strtotime($concours['date_debut'])), 0, 1);
        
        $pdf->Cell(50, 6, 'Date de fin :', 0, 0);
        $pdf->Cell(0, 6, date('d/m/Y', strtotime($concours['date_fin'])), 0, 1);
        
        $pdf->Cell(50, 6, 'Copies déposées :', 0, 0);
        $pdf->Cell(0, 6, $concours['copies_deposees'], 0, 1);
        
        $pdf->Cell(50, 6, 'Statut :', 0, 0);
        $pdf->Cell(0, 6, ($concours['date_fin'] >= date('Y-m-d')) ? 'En cours' : 'Terminé', 0, 1);
        
        $pdf->Ln(5);
    }
}

// Générer le PDF
$nom_fichier = 'fiche_inscription_' .
               preg_replace('/[^a-zA-Z0-9_-]/', '_', $candidat['nom']) . '_' .
               preg_replace('/[^a-zA-Z0-9_-]/', '_', $candidat['prenom']) . '.pdf';

try {
    $pdf->Output($nom_fichier, 'D');
} catch (Exception $e) {
    die("Erreur lors de la génération du PDF : " . $e->getMessage());
}