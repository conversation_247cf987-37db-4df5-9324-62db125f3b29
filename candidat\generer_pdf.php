<?php
require_once __DIR__ . '/../vendor/tecnickcom/tcpdf/tcpdf.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';

verifier_connexion();
verifier_role('candidat');

$id_candidat = utilisateur()['id'];

// Récupérer les informations du candidat
$stmt_candidat = $pdo->prepare("
    SELECT nom, prenom, email, specialite, date_inscription
    FROM users
    WHERE id = ?
");
$stmt_candidat->execute([$id_candidat]);
$candidat_info = $stmt_candidat->fetch();

if (!$candidat_info) {
    die('Informations candidat non trouvées');
}

// Récupérer les concours auxquels le candidat est inscrit
$stmt_concours = $pdo->prepare("
    SELECT DISTINCT c.id, c.titre, c.date_debut, c.date_fin, c.date_concours, c.description,
           ic.date_inscription as date_inscription_concours
    FROM concours c
    JOIN inscription_concours ic ON c.id = ic.id_concours
    WHERE ic.id_candidat = ?
    ORDER BY c.date_concours DESC
");
$stmt_concours->execute([$id_candidat]);
$concours_inscrits = $stmt_concours->fetchAll();

// Créer le PDF
$pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

// Définir les informations du document
$pdf->SetCreator(PDF_CREATOR);
$pdf->SetAuthor('Système de Gestion des Concours');
$pdf->SetTitle('Fiche d\'Inscription - ' . $candidat_info['prenom'] . ' ' . $candidat_info['nom']);

// Définir les marges
$pdf->SetMargins(15, 15, 15);
$pdf->SetHeaderMargin(5);
$pdf->SetFooterMargin(10);

// Ajouter une page
$pdf->AddPage();

// En-tête du document
$pdf->SetFont('helvetica', 'B', 16);
$pdf->Cell(0, 15, 'FICHE D\'INSCRIPTION', 0, 1, 'C');
$pdf->SetFont('helvetica', 'B', 14);
$pdf->Cell(0, 10, 'Plateforme de Concours à Correction Anonyme', 0, 1, 'C');
$pdf->Ln(10);

// Informations du candidat
$pdf->SetFont('helvetica', 'B', 12);
$pdf->Cell(0, 8, 'INFORMATIONS PERSONNELLES', 0, 1, 'L');
$pdf->Line(15, $pdf->GetY(), 195, $pdf->GetY());
$pdf->Ln(5);

$pdf->SetFont('helvetica', '', 11);
$pdf->Cell(40, 7, 'Nom :', 0, 0, 'L');
$pdf->SetFont('helvetica', 'B', 11);
$pdf->Cell(0, 7, strtoupper($candidat_info['nom']), 0, 1, 'L');

$pdf->SetFont('helvetica', '', 11);
$pdf->Cell(40, 7, 'Prénom :', 0, 0, 'L');
$pdf->SetFont('helvetica', 'B', 11);
$pdf->Cell(0, 7, ucfirst($candidat_info['prenom']), 0, 1, 'L');

$pdf->SetFont('helvetica', '', 11);
$pdf->Cell(40, 7, 'Email :', 0, 0, 'L');
$pdf->SetFont('helvetica', 'B', 11);
$pdf->Cell(0, 7, $candidat_info['email'], 0, 1, 'L');

if ($candidat_info['specialite']) {
    $pdf->SetFont('helvetica', '', 11);
    $pdf->Cell(40, 7, 'Spécialité :', 0, 0, 'L');
    $pdf->SetFont('helvetica', 'B', 11);
    $pdf->Cell(0, 7, $candidat_info['specialite'], 0, 1, 'L');
}

$pdf->SetFont('helvetica', '', 11);
$pdf->Cell(40, 7, 'Date d\'inscription :', 0, 0, 'L');
$pdf->SetFont('helvetica', 'B', 11);
$pdf->Cell(0, 7, date('d/m/Y', strtotime($candidat_info['date_inscription'])), 0, 1, 'L');

$pdf->Ln(10);

// Liste des concours
$pdf->SetFont('helvetica', 'B', 12);
$pdf->Cell(0, 8, 'CONCOURS INSCRITS', 0, 1, 'L');
$pdf->Line(15, $pdf->GetY(), 195, $pdf->GetY());
$pdf->Ln(5);

if (empty($concours_inscrits)) {
    $pdf->SetFont('helvetica', 'I', 11);
    $pdf->Cell(0, 7, 'Aucun concours inscrit pour le moment.', 0, 1, 'L');
} else {
    foreach ($concours_inscrits as $concours) {
        // Cadre pour chaque concours
        $pdf->SetFillColor(245, 245, 245);
        $pdf->Rect(15, $pdf->GetY(), 180, 35, 'F');

        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, $concours['titre'], 0, 1, 'L');

        $pdf->SetFont('helvetica', '', 10);
        if ($concours['description']) {
            $pdf->MultiCell(0, 5, 'Description: ' . $concours['description'], 0, 'L');
        }

        $pdf->Cell(90, 6, 'Période d\'inscription: ' . date('d/m/Y', strtotime($concours['date_debut'])) . ' - ' . date('d/m/Y', strtotime($concours['date_fin'])), 0, 0, 'L');
        $pdf->Cell(90, 6, 'Date du concours: ' . ($concours['date_concours'] ? date('d/m/Y', strtotime($concours['date_concours'])) : 'Non définie'), 0, 1, 'L');

        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(0, 6, 'Inscrit le: ' . date('d/m/Y à H:i', strtotime($concours['date_inscription_concours'])), 0, 1, 'L');

        $pdf->Ln(8);
    }
}

$pdf->Ln(10);

// Pied de page avec informations importantes
$pdf->SetFont('helvetica', 'B', 10);
$pdf->Cell(0, 6, 'INFORMATIONS IMPORTANTES', 0, 1, 'L');
$pdf->Line(15, $pdf->GetY(), 195, $pdf->GetY());
$pdf->Ln(3);

$pdf->SetFont('helvetica', '', 9);
$pdf->MultiCell(0, 4, '• Cette fiche d\'inscription fait foi de votre participation aux concours mentionnés ci-dessus.', 0, 'L');
$pdf->MultiCell(0, 4, '• Vous devez déposer vos copies dans les délais impartis via la plateforme.', 0, 'L');
$pdf->MultiCell(0, 4, '• La correction est anonyme, votre identité ne sera pas révélée aux correcteurs.', 0, 'L');
$pdf->MultiCell(0, 4, '• Les résultats seront publiés sur la plateforme une fois la correction terminée.', 0, 'L');

$pdf->Ln(5);
$pdf->SetFont('helvetica', 'I', 8);
$pdf->Cell(0, 4, 'Document généré le ' . date('d/m/Y à H:i'), 0, 1, 'R');

// Générer le PDF
$pdf->Output('fiche_inscription_' . $candidat_info['nom'] . '_' . $candidat_info['prenom'] . '.pdf', 'I');
?>
