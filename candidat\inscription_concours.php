<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';

verifier_connexion();
verifier_role('candidat');

// Traitement de l'inscription
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'afficher_formulaire') {
        // Afficher le formulaire d'inscription pour le concours sélectionné
        $id_concours = intval($_POST['id_concours']);
        
        // Récupérer les informations du concours
        $stmt = $pdo->prepare("SELECT * FROM concours WHERE id = ?");
        $stmt->execute([$id_concours]);
        $concours_selectionne = $stmt->fetch();
        
        if (!$concours_selectionne) {
            $error = "Concours non trouvé.";
        }
    } elseif (isset($_POST['action']) && $_POST['action'] === 'inscrire') {
        // Traiter l'inscription avec les informations du formulaire
        $id_concours = intval($_POST['id_concours']);
        $id_candidat = $_SESSION['user']['id'];
        $niveau_etudes = trim($_POST['niveau_etudes'] ?? '');
        $etablissement = trim($_POST['etablissement'] ?? '');
        $motivation = trim($_POST['motivation'] ?? '');
        $accepte_conditions = isset($_POST['accepte_conditions']) ? 1 : 0;
        
        // Validation des champs
        if (empty($niveau_etudes) || empty($etablissement) || empty($motivation) || !$accepte_conditions) {
            $error = "Tous les champs sont obligatoires et vous devez accepter les conditions.";
            
            // Récupérer les informations du concours pour réafficher le formulaire
            $stmt = $pdo->prepare("SELECT * FROM concours WHERE id = ?");
            $stmt->execute([$id_concours]);
            $concours_selectionne = $stmt->fetch();
        } else {
            try {
                // Vérifier si le candidat est déjà inscrit à ce concours
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM inscription_concours WHERE id_candidat = ? AND id_concours = ?");
                $stmt->execute([$id_candidat, $id_concours]);
                if ($stmt->fetchColumn() > 0) {
                    $error = "Vous êtes déjà inscrit à ce concours.";
                } else {
                    $pdo->beginTransaction();
                    
                    // Insérer l'inscription avec les informations supplémentaires
                    $stmt = $pdo->prepare("
                        INSERT INTO inscription_concours 
                        (id_candidat, id_concours, niveau_etudes, etablissement, motivation, accepte_conditions) 
                        VALUES (?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([$id_candidat, $id_concours, $niveau_etudes, $etablissement, $motivation, $accepte_conditions]);
                    
                    $pdo->commit();
                    
                    // Stocker l'ID du concours dans la session pour le message de succès
                    $_SESSION['concours_inscrit'] = $id_concours;
                    header('Location: inscription_concours.php?success=1');
                    exit;
                }
            } catch (PDOException $e) {
                $pdo->rollBack();
                $error = "Une erreur est survenue lors de l'inscription : " . $e->getMessage();
            }
        }
    }
}

// Récupération des concours disponibles
$concours = $pdo->query("
    SELECT c.*, 
           (SELECT COUNT(*) FROM inscription_concours ic WHERE ic.id_concours = c.id AND ic.id_candidat = {$_SESSION['user']['id']}) as est_inscrit
    FROM concours c
    WHERE c.date_fin >= CURRENT_DATE
    ORDER BY c.date_debut DESC
")->fetchAll();

// Récupération des concours auxquels le candidat est inscrit
$concours_inscrits = $pdo->query("
    SELECT c.*, ic.date_inscription
    FROM concours c
    JOIN inscription_concours ic ON c.id = ic.id_concours
    WHERE ic.id_candidat = {$_SESSION['user']['id']}
    ORDER BY c.date_debut DESC
")->fetchAll();
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Inscription aux Concours</title>
    <link rel="stylesheet" href="../public/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h2>Inscription aux Concours</h2>
        
        <?php if (isset($_GET['success'])): ?>
            <div class="success-message">
                <p>Inscription réussie au concours !</p>
                <a href="generer_pdf.php" class="btn-imprimer">
                    <i class="fas fa-print"></i> Imprimer la fiche d'inscription
                </a>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="error-message">
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <div class="concours-section">
            <h3>Concours Disponibles</h3>
            <div class="concours-grid">
                <?php foreach ($concours as $c): ?>
                    <div class="concours-card">
                        <h3><?= htmlspecialchars($c['titre']) ?></h3>
                        <div class="date-info">
                            <i class="fas fa-calendar-alt"></i>
                            <p><strong>Début:</strong> <?= date('d/m/Y', strtotime($c['date_debut'])) ?></p>
                        </div>
                        <div class="date-info">
                            <i class="fas fa-calendar-check"></i>
                            <p><strong>Fin:</strong> <?= date('d/m/Y', strtotime($c['date_fin'])) ?></p>
                        </div>
                        <?php if ($c['description']): ?>
                            <p><?= nl2br(htmlspecialchars($c['description'])) ?></p>
                        <?php endif; ?>
                        
                        <?php if ($c['est_inscrit']): ?>
                            <button class="btn-inscrit" disabled>Déjà inscrit</button>
                        <?php elseif (isset($concours_selectionne) && $concours_selectionne['id'] == $c['id']): ?>
                            <!-- Formulaire d'inscription détaillé -->
                            <form method="POST" class="inscription-form">
                                <input type="hidden" name="action" value="inscrire">
                                <input type="hidden" name="id_concours" value="<?= $c['id'] ?>">
                                
                                <div class="form-group">
                                    <label for="niveau_etudes">Niveau d'études</label>
                                    <select name="niveau_etudes" id="niveau_etudes" required>
                                        <option value="">Sélectionnez votre niveau</option>
                                        <option value="Bac">Baccalauréat</option>
                                        <option value="Bac+1">Bac+1</option>
                                        <option value="Bac+2">Bac+2</option>
                                        <option value="Bac+3">Bac+3</option>
                                        <option value="Bac+4">Bac+4</option>
                                        <option value="Bac+5">Bac+5</option>
                                        <option value="Doctorat">Doctorat</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="etablissement">Établissement</label>
                                    <input type="text" name="etablissement" id="etablissement" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="motivation">Motivation</label>
                                    <textarea name="motivation" id="motivation" rows="4" required></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" name="accepte_conditions" required>
                                        J'accepte les conditions de participation au concours
                                    </label>
                                </div>
                                
                                <div class="form-buttons">
                                    <button type="submit" class="btn-inscrire">Confirmer l'inscription</button>
                                    <a href="inscription_concours.php" class="btn-annuler">Annuler</a>
                                </div>
                            </form>
                        <?php else: ?>
                            <form method="POST">
                                <input type="hidden" name="action" value="afficher_formulaire">
                                <input type="hidden" name="id_concours" value="<?= $c['id'] ?>">
                                <button type="submit" class="btn-inscrire">S'inscrire</button>
                            </form>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="concours-section">
            <h3>Mes Concours</h3>
            <div class="concours-grid">
                <?php foreach ($concours_inscrits as $c): ?>
                    <div class="concours-card">
                        <h3><?= htmlspecialchars($c['titre']) ?></h3>
                        <div class="date-info">
                            <i class="fas fa-calendar-alt"></i>
                            <p><strong>Début:</strong> <?= date('d/m/Y', strtotime($c['date_debut'])) ?></p>
                        </div>
                        <div class="date-info">
                            <i class="fas fa-calendar-check"></i>
                            <p><strong>Fin:</strong> <?= date('d/m/Y', strtotime($c['date_fin'])) ?></p>
                        </div>
                        <div class="date-info">
                            <i class="fas fa-user-check"></i>
                            <p><strong>Inscrit le:</strong> <?= date('d/m/Y H:i', strtotime($c['date_inscription'])) ?></p>
                        </div>
                        <?php if ($c['description']): ?>
                            <p><?= nl2br(htmlspecialchars($c['description'])) ?></p>
                        <?php endif; ?>
                        <button class="btn-inscrit">Inscrit</button>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <a href="dashboard.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Retour au tableau de bord
        </a>
    </div>
</body>
</html> 
<style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        h2 {
            color: #ffffff;
            text-align: center;
            margin-bottom: 40px;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            animation: fadeInDown 0.8s ease-out;
        }

        .concours-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 35px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 40px;
            border: 1px solid rgba(255,255,255,0.2);
            animation: fadeInUp 0.8s ease-out;
        }

        .concours-section h3 {
            color: #2c3e50;
            font-size: 1.8rem;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .concours-section h3::before {
            content: "🎓";
            font-size: 1.5rem;
        }

        .concours-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 25px;
        }

        .concours-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border: none;
            border-radius: 15px;
            padding: 25px;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .concours-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .concours-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .concours-card h3 {
            margin-top: 0;
            color: #2c3e50;
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .concours-card p {
            color: #6c757d;
            margin: 8px 0;
            font-size: 0.95rem;
        }

        .concours-card p strong {
            color: #495057;
            font-weight: 600;
        }

        .date-info {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 10px 0;
        }

        .date-info i {
            color: #667eea;
            width: 16px;
        }

        .btn-inscrire {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            width: 100%;
            margin-top: 15px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-inscrire::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-inscrire:hover::before {
            left: 100%;
        }

        .btn-inscrire:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
        }

        .btn-inscrire:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-inscrit {
            background: linear-gradient(135deg, #17a2b8, #20c997);
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            cursor: default;
            width: 100%;
            margin-top: 15px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-inscrit::before {
            content: "✓";
            font-weight: bold;
        }

        .success-message {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            border: 1px solid #c3e6cb;
            animation: slideInDown 0.5s ease-out;
        }

        .error-message {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            text-align: center;
            border: 1px solid #f5c6cb;
            animation: shake 0.5s ease-in-out;
        }

        .btn-imprimer {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            text-decoration: none;
            margin-top: 15px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
        }

        .btn-imprimer:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        /* Styles pour le formulaire d'inscription */
        .inscription-form {
            margin-top: 20px;
            background: rgba(248, 249, 250, 0.8);
            padding: 25px;
            border-radius: 15px;
            border: 2px dashed #667eea;
            animation: fadeIn 0.5s ease-out;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 1rem;
        }

        .form-group input[type="text"],
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-family: inherit;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input[type="text"]:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-group input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
        }

        .form-buttons {
            display: flex;
            gap: 15px;
            margin-top: 25px;
        }

        .btn-annuler {
            background: linear-gradient(135deg, #6c757d, #5a6268);
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            text-decoration: none;
            text-align: center;
            flex: 1;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-annuler:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(108, 117, 125, 0.3);
        }

        .form-buttons .btn-inscrire {
            flex: 2;
            margin-top: 0;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            margin-top: 30px;
            padding: 12px 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.2);
            transform: translateX(-5px);
        }

        /* Animations */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideInDown {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
            }

            h2 {
                font-size: 2rem;
            }

            .concours-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .concours-section {
                padding: 25px 20px;
            }

            .form-buttons {
                flex-direction: column;
            }

            .btn-annuler {
                margin-top: 10px;
            }
        }

        .btn-inscrit::before {
            content: "✓";
            font-weight: bold;
        }

        .success-message {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            border: 1px solid #c3e6cb;
            animation: slideInDown 0.5s ease-out;
        }

        .error-message {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            text-align: center;
            border: 1px solid #f5c6cb;
            animation: shake 0.5s ease-in-out;
        }

        .btn-imprimer {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            text-decoration: none;
            margin-top: 15px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
        }

        .btn-imprimer:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        /* Styles pour le formulaire d'inscription */
        .inscription-form {
            margin-top: 20px;
            background: rgba(248, 249, 250, 0.8);
            padding: 25px;
            border-radius: 15px;
            border: 2px dashed #667eea;
            animation: fadeIn 0.5s ease-out;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 1rem;
        }

        .form-group input[type="text"],
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-family: inherit;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input[type="text"]:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-group input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
        }

        .form-buttons {
            display: flex;
            gap: 15px;
            margin-top: 25px;
        }

        .btn-annuler {
            background: linear-gradient(135deg, #6c757d, #5a6268);
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            text-decoration: none;
            text-align: center;
            flex: 1;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-annuler:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(108, 117, 125, 0.3);
        }

        .form-buttons .btn-inscrire {
            flex: 2;
            margin-top: 0;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            margin-top: 30px;
            padding: 12px 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.2);
            transform: translateX(-5px);
        }

        /* Animations */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideInDown {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
            }

            h2 {
                font-size: 2rem;
            }

            .concours-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .concours-section {
                padding: 25px 20px;
            }

            .form-buttons {
                flex-direction: column;
            }

            .btn-annuler {
                margin-top: 10px;
            }
        }
    </style>