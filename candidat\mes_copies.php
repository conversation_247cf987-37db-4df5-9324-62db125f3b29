<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';
verifier_connexion();
verifier_role('candidat');

// Définir le titre de la page avant d'inclure le header
$page_title = "Mes copies déposées";
require_once '../includes/header.php';

$id_candidat = $_SESSION['user']['id'];

$stmt = $pdo->prepare("
    SELECT c.*, con.titre, con.date_fin
    FROM copie c
    JOIN concours con ON c.id_concours = con.id
    WHERE id_candidat = ?
    ORDER BY date_depot DESC
");
$stmt->execute([$id_candidat]);
$copies = $stmt->fetchAll();
?>

<h2>Mes copies déposées</h2>

<?php if (empty($copies)): ?>
    <div class="message info">
        Vous n'avez déposé aucune copie pour le moment.
        <a href="depot.php">Déposer une copie</a>
    </div>
<?php else: ?>
    <div class="table-responsive">
        <table class="copies-table">
            <thead>
                <tr>
                    <th>Concours</th>
                    <th>Identifiant Anonyme</th>
                    <th>Fichier</th>
                    <th>Date de dépôt</th>
                    <th>Statut</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($copies as $c): ?>
                    <tr>
                        <td><?= htmlspecialchars($c['titre']) ?></td>
                        <td><?= htmlspecialchars($c['identifiant_anon']) ?></td>
                        <td>
                            <?php if (file_exists("../public/uploads/{$c['fichier_path']}")): ?>
                                <a href="../public/uploads/<?= htmlspecialchars($c['fichier_path']) ?>" target="_blank" class="btn-view">
                                    <i class="fas fa-file-pdf"></i> Voir
                                </a>
                            <?php else: ?>
                                <span class="text-danger">Fichier non disponible</span>
                            <?php endif; ?>
                        </td>
                        <td><?= formater_date($c['date_depot']) ?></td>
                        <td>
                            <span class="badge <?= $c['statut'] === 'corrigee' ? 'badge-success' : 'badge-warning' ?>">
                                <?= ucfirst(htmlspecialchars($c['statut'])) ?>
                            </span>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php endif; ?>

<style>
    .message {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
        text-align: center;
    }
    
    .message.info {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    
    .table-responsive {
        overflow-x: auto;
        margin-bottom: 20px;
    }
    
    .copies-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
        background-color: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .copies-table th, 
    .copies-table td {
        padding: 12px 15px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }
    
    .copies-table th {
        background-color: #f8f9fa;
        font-weight: bold;
        color: #495057;
    }
    
    .copies-table tr:hover {
        background-color: #f5f5f5;
    }
    
    .btn-view {
        display: inline-block;
        padding: 6px 12px;
        background-color: #007bff;
        color: white;
        border-radius: 4px;
        text-decoration: none;
        font-size: 14px;
    }
    
    .btn-view:hover {
        background-color: #0069d9;
        text-decoration: none;
        color: white;
    }
    
    .badge {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .badge-success {
        background-color: #28a745;
        color: white;
    }
    
    .badge-warning {
        background-color: #ffc107;
        color: #212529;
    }
    
    .text-danger {
        color: #dc3545;
    }
    
    @media (max-width: 768px) {
        .copies-table {
            font-size: 14px;
        }
        
        .copies-table th, 
        .copies-table td {
            padding: 8px 10px;
        }
        
        .btn-view {
            padding: 4px 8px;
            font-size: 12px;
        }
    }
</style>

<?php require_once '../includes/footer.php'; ?>
