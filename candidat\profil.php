<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';
verifier_connexion();
verifier_role('candidat');

// Définir le titre de la page avant d'inclure le header
$page_title = "Mon profil";
require_once '../includes/header.php';

$id = $_SESSION['user']['id'];
$message = '';
$type_message = '';

if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $message = "Erreur de sécurité. Veuillez réessayer.";
        $type_message = "error";
    } else {
        $nom = trim($_POST['nom'] ?? '');
        $prenom = trim($_POST['prenom'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        if (empty($nom) || empty($prenom) || empty($email)) {
            $message = "Tous les champs sont obligatoires.";
            $type_message = "error";
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $message = "L'adresse email n'est pas valide.";
            $type_message = "error";
        } else {
            try {
                $pdo->beginTransaction();
                
                // Vérifier si l'email existe déjà pour un autre utilisateur
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                $stmt->execute([$email, $id]);
                if ($stmt->fetch()) {
                    $message = "Cette adresse email est déjà utilisée par un autre utilisateur.";
                    $type_message = "error";
                } else {
                    // Mise à jour des informations de base
                    $stmt = $pdo->prepare("UPDATE users SET nom = ?, prenom = ?, email = ? WHERE id = ?");
                    $stmt->execute([$nom, $prenom, $email, $id]);
                    
                    // Mise à jour du mot de passe si demandé
                    if (!empty($current_password) && !empty($new_password)) {
                        // Vérifier l'ancien mot de passe
                        $stmt = $pdo->prepare("SELECT mot_de_passe FROM users WHERE id = ?");
                        $stmt->execute([$id]);
                        $user_data = $stmt->fetch();
                        
                        if (!password_verify($current_password, $user_data['mot_de_passe'])) {
                            throw new Exception("Le mot de passe actuel est incorrect.");
                        }
                        
                        if (strlen($new_password) < 6) {
                            throw new Exception("Le nouveau mot de passe doit contenir au moins 6 caractères.");
                        }
                        
                        if ($new_password !== $confirm_password) {
                            throw new Exception("Les nouveaux mots de passe ne correspondent pas.");
                        }
                        
                        // Mettre à jour le mot de passe
                        $stmt = $pdo->prepare("UPDATE users SET mot_de_passe = ? WHERE id = ?");
                        $stmt->execute([password_hash($new_password, PASSWORD_DEFAULT), $id]);
                    }
                    
                    // Mettre à jour la session
                    $_SESSION['user']['nom'] = $nom;
                    $_SESSION['user']['prenom'] = $prenom;
                    $_SESSION['user']['email'] = $email;
                    
                    $pdo->commit();
                    $message = "Profil mis à jour avec succès.";
                    $type_message = "success";
                }
            } catch (Exception $e) {
                $pdo->rollBack();
                $message = "Erreur : " . $e->getMessage();
                $type_message = "error";
            }
        }
    }
}

// Récupérer les informations actuelles de l'utilisateur
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$id]);
$user = $stmt->fetch();
?>

<h2>Mon profil</h2>

<?php if ($message): ?>
    <div class="message <?= $type_message ?>">
        <?= htmlspecialchars($message) ?>
    </div>
<?php endif; ?>

<div class="profile-container">
    <form method="POST" class="profile-form">
        <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token'] ?>">
        
        <div class="form-section">
            <h3>Informations personnelles</h3>
            
            <div class="form-group">
                <label for="nom">Nom</label>
                <input type="text" id="nom" name="nom" value="<?= htmlspecialchars($user['nom']) ?>" required>
            </div>
            
            <div class="form-group">
                <label for="prenom">Prénom</label>
                <input type="text" id="prenom" name="prenom" value="<?= htmlspecialchars($user['prenom']) ?>" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" value="<?= htmlspecialchars($user['email']) ?>" required>
            </div>
        </div>
        
        <div class="form-section">
            <h3>Changer de mot de passe</h3>
            <p class="form-info">Laissez vide si vous ne souhaitez pas changer de mot de passe</p>
            
            <div class="form-group">
                <label for="current_password">Mot de passe actuel</label>
                <input type="password" id="current_password" name="current_password">
            </div>
            
            <div class="form-group">
                <label for="new_password">Nouveau mot de passe</label>
                <input type="password" id="new_password" name="new_password" minlength="6">
            </div>
            
            <div class="form-group">
                <label for="confirm_password">Confirmer le nouveau mot de passe</label>
                <input type="password" id="confirm_password" name="confirm_password">
            </div>
        </div>
        
        <div class="form-actions">
            <button type="submit" class="btn-primary">Mettre à jour</button>
        </div>
    </form>
</div>

<style>
    .message {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
        text-align: center;
    }
    
    .message.success {
        background-color: #d4edda;
        color: #155724;
    }
    
    .message.error {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .profile-container {
        max-width: 600px;
        margin: 0 auto;
    }
    
    .profile-form {
        background-color: #fff;
        padding: 25px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .form-section {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }
    
    .form-section:last-child {
        border-bottom: none;
    }
    
    .form-section h3 {
        margin-top: 0;
        margin-bottom: 15px;
        color: #333;
    }
    
    .form-info {
        color: #666;
        font-size: 0.9em;
        margin-bottom: 15px;
    }
    
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }
    
    .form-group input {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    
    .form-actions {
        text-align: right;
    }
    
    .btn-primary {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
    }
    
    .btn-primary:hover {
        background-color: #0069d9;
    }
    
    @media (max-width: 768px) {
        .profile-form {
            padding: 15px;
        }
    }
</style>

<?php require_once '../includes/footer.php'; ?>
