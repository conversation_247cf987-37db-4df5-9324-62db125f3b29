<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';
verifier_connexion();
verifier_role('candidat');

$id_candidat = utilisateur()['id'];

// Récupération des résultats par matière
$resultats = $pdo->prepare("
    SELECT 
        c.titre as concours_nom,
        m.nom as matiere_nom,
        m.coefficient,
        corr.note_total,
        corr.commentaire_global,
        u.nom as correcteur_nom,
        u.prenom as correcteur_prenom
    FROM copie cp
    JOIN concours c ON cp.id_concours = c.id
    JOIN matiere m ON cp.id_matiere = m.id
    JOIN attribution a ON cp.id = a.id_copie
    JOIN correction corr ON a.id = corr.id_attribution
    JOIN users u ON a.id_correcteur = u.id
    WHERE cp.id_candidat = ?
    ORDER BY c.titre, m.nom
");
$resultats->execute([$id_candidat]);
$resultats_liste = $resultats->fetchAll();

// Calcul des moyennes par concours
$moyennes = [];
foreach ($resultats_liste as $r) {
    if (!isset($moyennes[$r['concours_nom']])) {
        $moyennes[$r['concours_nom']] = [
            'total_notes' => 0,
            'total_coefficients' => 0
        ];
    }
    $moyennes[$r['concours_nom']]['total_notes'] += $r['note_total'] * $r['coefficient'];
    $moyennes[$r['concours_nom']]['total_coefficients'] += $r['coefficient'];
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Mes Résultats</title>
    <link rel="stylesheet" href="../public/css/style.css">
</head>
<body>
    <div class="resultats-container">
        <h2>Mes Résultats</h2>
        <a href="dashboard.php" class="back-link">← Retour</a>
        <a href="export_resultats_candidat.php" class="export-btn" style="margin-left: 15px;">Imprimer la fiche de résultats</a>

        <?php
        $current_concours = '';
        foreach ($resultats_liste as $r):
            if ($current_concours !== $r['concours_nom']):
                if ($current_concours !== '') echo '</div>'; // Fermer la section précédente
                $current_concours = $r['concours_nom'];
                $moyenne = $moyennes[$r['concours_nom']]['total_notes'] / $moyennes[$r['concours_nom']]['total_coefficients'];
        ?>
                <div class="concours-section">
                    <div class="concours-header">
                        <h3><?= htmlspecialchars($r['concours_nom']) ?></h3>
                        <div class="moyenne">
                            Moyenne générale: 
                            <span class="note <?= $moyenne >= 15 ? 'note-bonne' : ($moyenne >= 10 ? 'note-moyenne' : 'note-basse') ?>">
                                <?= number_format($moyenne, 2) ?>/20
                            </span>
                        </div>
                    </div>
        <?php endif; ?>

            <div class="matiere-resultat">
                <h4><?= htmlspecialchars($r['matiere_nom']) ?></h4>
                <div class="note <?= $r['note_total'] >= 15 ? 'note-bonne' : ($r['note_total'] >= 10 ? 'note-moyenne' : 'note-basse') ?>">
                    Note: <?= $r['note_total'] ?>/20 (Coefficient: <?= $r['coefficient'] ?>)
                </div>
                <?php if ($r['commentaire_global']): ?>
                    <div class="commentaire">
                        <?= nl2br(htmlspecialchars($r['commentaire_global'])) ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
        <?php if ($current_concours !== '') echo '</div>'; // Fermer la dernière section ?>
    </div>
</body>
</html> 
<style>
        .resultats-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .concours-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .concours-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }
        .matiere-resultat {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .note {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
        }
        .note-bonne { color: #27ae60; }
        .note-moyenne { color: #f39c12; }
        .note-basse { color: #e74c3c; }
        .commentaire {
            margin-top: 10px;
            padding: 10px;
            background: #fff;
            border-left: 3px solid #3498db;
        }
    </style>