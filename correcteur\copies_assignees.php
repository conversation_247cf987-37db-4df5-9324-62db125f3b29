<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';
verifier_connexion();
verifier_role('correcteur');

$id_correcteur = $_SESSION['user']['id'];

$stmt = $pdo->prepare("
    SELECT a.id AS id_attribution, c.identifiant_anon, c.fichier_path, con.titre, c.date_depot 
    FROM attribution a
    JOIN copie c ON a.id_copie = c.id
    JOIN concours con ON c.id_concours = con.id
    WHERE a.id_correcteur = ? AND a.id NOT IN (SELECT id_attribution FROM correction)
    ORDER BY c.date_depot DESC
");
$stmt->execute([$id_correcteur]);
$copies = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Copies assignées | Système de correction</title>
    <style>
        /* Reset de base */
        body, h1, h2, h3, p, table {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        body {
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }
        /* Conteneur principal */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        /* En-tête */
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        /* Contenu */
        .content {
            padding: 20px;
        }
        
        /* Message quand pas de copies */
        .no-copies {
            text-align: center;
            padding: 40px;
            background-color: #f8f9fa;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        /* Tableau */
        .copies-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .copies-table th {
            background-color: #3498db;
            color: white;
            padding: 12px;
            text-align: left;
        }
        
        .copies-table td {
            padding: 10px 12px;
            border-bottom: 1px solid #ddd;
        }
        
        .copies-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .copies-table tr:hover {
            background-color: #e9f7fe;
        }
        
        /* Boutons et liens */
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background-color: #2980b9;
        }
        
        .btn-back {
            background-color: #95a5a6;
        }
        
        .btn-back:hover {
            background-color: #7f8c8d;
        }
        
        .file-link {
            color: #3498db;
            text-decoration: none;
        }
        
        .file-link:hover {
            text-decoration: underline;
        }
        
        /* Badge pour ID anonyme */
        .badge {
            display: inline-block;
            padding: 3px 8px;
            background-color: #ecf0f1;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9em;
        }
        
        /* Pied de page */
        .footer {
            text-align: center;
            padding: 15px;
            border-top: 1px solid #eee;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Copies à corriger</h1>
        </div>
        
        <div class="content">
            <?php if (empty($copies)): ?>
                <div class="no-copies">
                    <h3>Aucune copie en attente de correction</h3>
                    <p>Vous n'avez actuellement aucune copie à corriger.</p>
                
                </div>
            <?php else: ?>
                <table class="copies-table">
                    <thead>
                        <tr>
                            <th>Concours</th>
                            <th>Identifiant Anonyme</th>
                            <th>Fichier</th>
                            <th>Date dépôt</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($copies as $c): ?>
                            <tr>
                                <td><?= htmlspecialchars($c['titre']) ?></td>
                                <td><span class="badge"><?= $c['identifiant_anon'] ?></span></td>
                                <td><a href="../public/uploads/<?= $c['fichier_path'] ?>" class="file-link" target="_blank">Voir la copie</a></td>
                                <td><?= date('d/m/Y H:i', strtotime($c['date_depot'])) ?></td>
                                <td><a href="corriger.php?id=<?= $c['id_attribution'] ?>" class="btn">Corriger</a></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
            
            <div class="footer">
                <a href="dashboard.php" class="btn btn-back">Retour</a>
            </div>
        </div>
    </div>
</body>
</html>