<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';
verifier_connexion();
verifier_role('correcteur');

$id_correcteur = utilisateur()['id'];
$id_attribution = $_GET['id'] ?? null;

if (!$id_attribution) {
    die("ID de l'attribution manquant.");
}

// Vérifie que la copie est bien attribuée à ce correcteur
$stmt = $pdo->prepare("
    SELECT a.id AS id_attribution, c.id AS id_copie, c.titre AS concours, cp.identifiant_anon, cp.fichier_path, cp.id_concours
    FROM attribution a
    JOIN copie cp ON a.id_copie = cp.id
    JOIN concours c ON cp.id_concours = c.id
    WHERE a.id_correcteur = ? AND a.id = ?
");
$stmt->execute([$id_correcteur, $id_attribution]);
$attribution = $stmt->fetch();

if (!$attribution) {
    die("Vous n'avez pas accès à cette copie.");
}

$id_copie = $attribution['id_copie'];
$id_concours = $attribution['id_concours'];

// Vérifie si déjà corrigée
$stmt = $pdo->prepare("SELECT * FROM correction WHERE id_attribution = ?");
$stmt->execute([$id_attribution]);
$correction_existante = $stmt->fetch();

// Récupérer les critères pour ce concours
$grille = $pdo->prepare("
    SELECT id, critere, bareme
    FROM grille_evaluation
    WHERE id_concours = ?
");
$grille->execute([$id_concours]);
$criteres = $grille->fetchAll();

$total_bareme = array_sum(array_column($criteres, 'bareme')); // Calcul du total des barèmes

// Initialiser les variables pour l'affichage
$note_total = 0;
$commentaire = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$correction_existante) {
    $commentaire = $_POST['commentaire'] ?? '';
    $notes = $_POST['notes'] ?? [];
    $note_total = array_sum($notes); // Calcul automatique de la note totale

    try {
        $pdo->beginTransaction();

        // Enregistrer la correction
        $stmt = $pdo->prepare("INSERT INTO correction (id_attribution, commentaire_global, note_total) VALUES (?, ?, ?)");
        $stmt->execute([$id_attribution, $commentaire, $note_total]);
        $id_correction = $pdo->lastInsertId();

        // Enregistrer les notes détaillées
        $stmt = $pdo->prepare("INSERT INTO note_detaillee (id_correction, id_critere, note) VALUES (?, ?, ?)");
        foreach ($notes as $id_critere => $note) {
            $stmt->execute([$id_correction, $id_critere, $note]);
        }

        // Mettre à jour la copie
        $pdo->prepare("UPDATE copie SET statut = 'corrigee' WHERE id = ?")->execute([$id_copie]);

        $pdo->commit();
        header("Location: copies_assignees.php?success=1");
        exit;

    } catch (Exception $e) {
        $pdo->rollBack();
        die("Erreur lors de la correction : " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Corriger une copie</title>
    <link rel="stylesheet" href="../public/css/style.css">
    <style>
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
<h2>Correction de la copie : <?= htmlspecialchars($attribution['identifiant_anon']) ?></h2>
<p>Concours : <strong><?= htmlspecialchars($attribution['concours']) ?></strong></p>

<p><a href="../public/uploads/<?= htmlspecialchars($attribution['fichier_path']) ?>" target="_blank" style="color:blue;">📄 Ouvrir la copie</a></p>

<?php if ($correction_existante): ?>
    <p class="success">Cette copie a déjà été corrigée.</p>
<?php elseif (empty($criteres)): ?>
    <p class="error">Aucun critère de correction n'est défini pour ce concours. Contactez l'administrateur.</p>
<?php else: ?>
    <form method="POST">
        <fieldset>
            <legend><strong>Notes par critère</strong></legend>
            <?php foreach ($criteres as $crit): ?>
                <label><?= htmlspecialchars($crit['critere']) ?> (sur <?= $crit['bareme'] ?>) :</label><br>
                <input type="number" name="notes[<?= $crit['id'] ?>]" min="0" max="<?= $crit['bareme'] ?>" step="0.5" required><br><br>
            <?php endforeach; ?>
        </fieldset>

        <p><strong>Note totale (calculée) : </strong><span id="note-totale"><?= $note_total > 0 ? $note_total : 'Non calculée encore' ?></span>/<?= $total_bareme ?></p>
        <input type="hidden" name="note_total" value="<?= $note_total ?>" id="note-total-input">

        <label><strong>Commentaire global :</strong></label><br>
        <textarea name="commentaire" rows="4" cols="50" placeholder="Entrez votre commentaire ici..."><?= htmlspecialchars($commentaire ?? '') ?></textarea><br><br>

        <button type="submit">✅ Soumettre la correction</button>
    </form>

    <script>
        // Calcul automatique de la note totale
        document.addEventListener('DOMContentLoaded', function() {
            const noteInputs = document.querySelectorAll('input[name^="notes["]');
            const noteTotaleSpan = document.getElementById('note-totale');
            const noteTotaleInput = document.getElementById('note-total-input');

            function calculerNoteTotal() {
                let total = 0;
                noteInputs.forEach(input => {
                    const valeur = parseFloat(input.value) || 0;
                    total += valeur;
                });

                noteTotaleSpan.textContent = total > 0 ? total.toFixed(1) : 'Non calculée encore';
                noteTotaleInput.value = total;
            }

            // Écouter les changements sur tous les champs de notes
            noteInputs.forEach(input => {
                input.addEventListener('input', calculerNoteTotal);
                input.addEventListener('change', calculerNoteTotal);
            });
        });
    </script>
<?php endif; ?>

<p><a href="copies_assignees.php">← Retour</a></p>
</body>
</html>