<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';
verifier_connexion();
verifier_role('correcteur');

$id_correcteur = $_SESSION['user']['id'];

$total = $pdo->prepare("SELECT COUNT(*) FROM attribution WHERE id_correcteur = ?");
$total->execute([$id_correcteur]);
$nb_total = $total->fetchColumn();

$corrigees = $pdo->prepare("
    SELECT COUNT(*) FROM correction 
    WHERE id_attribution IN (SELECT id FROM attribution WHERE id_correcteur = ?)
");
$corrigees->execute([$id_correcteur]);
$nb_corrigees = $corrigees->fetchColumn();
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Correcteur</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
</head>
<body>
    <div class="container">

        <div class="welcome-section">
            <h1>
                <i class="fas fa-user-graduate"></i>
                Bienvenue <?= htmlspecialchars($_SESSION['user']['prenom']) ?>
            </h1>
            <p>Tableau de bord du correcteur</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="stat-number"><?= $nb_total ?></div>
                <div class="stat-label">Copies assignées</div>

                <?php if ($nb_total > 0): ?>
                <div class="progress-section">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: <?= ($nb_corrigees / $nb_total) * 100 ?>%"></div>
                    </div>
                    <div class="progress-text">
                        <?= round(($nb_corrigees / $nb_total) * 100, 1) ?>% terminé
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number"><?= $nb_corrigees ?></div>
                <div class="stat-label">Copies corrigées</div>

                <?php if ($nb_total > 0): ?>
                <div class="progress-section">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: <?= ($nb_corrigees / $nb_total) * 100 ?>%"></div>
                    </div>
                    <div class="progress-text">
                        <?= $nb_total - $nb_corrigees ?> restantes
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="actions-section">
            <h2>Actions rapides</h2>
            <div class="action-grid">
                <a href="copies_assignees.php" class="action-card">
                    <i class="fas fa-tasks"></i>
                    <div class="action-content">
                        <h3>Mes copies assignées</h3>
                        <p>Voir et corriger les copies qui me sont attribuées</p>
                    </div>
                </a>

                <a href="historique.php" class="action-card">
                    <i class="fas fa-history"></i>
                    <div class="action-content">
                        <h3>Historique des corrections</h3>
                        <p>Consulter mes corrections précédentes</p>
                    </div>
                </a>

                <a href="../public/deconnexion.php" class="action-card logout-card">
                    <i class="fas fa-sign-out-alt"></i>
                    <div class="action-content">
                        <h3>Se déconnecter</h3>
                        <p>Terminer ma session de correction</p>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <script>
        // Animation des barres de progression
        document.addEventListener('DOMContentLoaded', function() {
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });
        });
    </script>
</body>
</html>
<style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            line-height: 1.6;
            animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .welcome-section {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(8px);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.06);
            margin-bottom: 25px;
            border: 1px solid rgba(255,255,255,0.3);
            text-align: center;
            animation: slideInDown 0.8s ease-out;
        }

        .welcome-section h1 {
            color: #2c3e50;
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .welcome-section h1 i {
            color: #667eea;
            animation: pulse 2s infinite;
        }

        .welcome-section p {
            color: #6c757d;
            font-size: 1rem;
            font-weight: 400;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
            animation: slideInUp 0.8s ease-out 0.2s both;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(8px);
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 6px 20px rgba(0,0,0,0.06);
            border: 1px solid rgba(255,255,255,0.3);
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 25px rgba(0,0,0,0.1);
        }

        .stat-icon {
            font-size: 2.2rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 2.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            line-height: 1;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.95rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .actions-section {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(8px);
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 6px 20px rgba(0,0,0,0.06);
            border: 1px solid rgba(255,255,255,0.3);
            animation: slideInUp 0.8s ease-out 0.4s both;
        }

        .actions-section h2 {
            color: #2c3e50;
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .actions-section h2::before {
            content: "⚡";
            font-size: 1.2rem;
        }

        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 15px;
        }

        .action-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            padding: 18px;
            border-radius: 10px;
            text-decoration: none;
            color: #2c3e50;
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .action-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
            border-color: #667eea;
            color: #2c3e50;
            text-decoration: none;
        }

        .action-card i {
            font-size: 1.6rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            min-width: 32px;
        }

        .action-card .action-content h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 4px;
            color: #2c3e50;
        }

        .action-card .action-content p {
            color: #6c757d;
            font-size: 0.85rem;
            margin: 0;
        }

        .logout-card {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border-color: transparent;
        }

        .logout-card:hover {
            border-color: #ffffff;
            color: white;
            background: linear-gradient(135deg, #c0392b, #a93226);
        }

        .logout-card i {
            color: white !important;
            -webkit-text-fill-color: white !important;
        }

        .logout-card .action-content h3,
        .logout-card .action-content p {
            color: white;
        }

        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            .welcome-section {
                padding: 25px;
            }

            .welcome-section h1 {
                font-size: 2rem;
                flex-direction: column;
                gap: 10px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .action-grid {
                grid-template-columns: 1fr;
            }

            .stat-number {
                font-size: 2.5rem;
            }
        }

        .progress-section {
            margin-top: 20px;
            padding: 20px;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 15px;
        }

        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 12px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 10px;
            transition: width 0.8s ease;
        }

        .progress-text {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
            text-align: center;
        }
    </style>
