<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';
verifier_connexion();
verifier_role('correcteur');

$id_correcteur = utilisateur()['id'];

// Récupération des concours pour le menu déroulant
$concours_stmt = $pdo->query("SELECT id, titre FROM concours ORDER BY titre");
$concours_list = $concours_stmt->fetchAll();

// Récupération des filtres
$filtre_concours = $_GET['concours'] ?? '';
$filtre_date = $_GET['date'] ?? '';

$conditions = ["a.id_correcteur = ?"];
$params = [$id_correcteur];

if (!empty($filtre_concours)) {
    $conditions[] = "con.id = ?";
    $params[] = $filtre_concours;
}
if (!empty($filtre_date)) {
    $conditions[] = "DATE(corr.date_correction) = ?";
    $params[] = $filtre_date;
}

$sql = "
    SELECT 
        c.identifiant_anon,
        con.titre AS concours,
        corr.note_total,
        corr.commentaire_global,
        corr.date_correction
    FROM correction corr
    JOIN attribution a ON corr.id_attribution = a.id
    JOIN copie c ON a.id_copie = c.id
    JOIN concours con ON c.id_concours = con.id
    WHERE " . implode(' AND ', $conditions) . "
    ORDER BY corr.date_correction DESC
";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$corrections = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Historique des corrections</title>
    <link rel="stylesheet" href="../public/css/style.css">
</head>
<body>
<h2>Historique de mes corrections</h2>

<form method="GET">
    <label for="concours">Filtrer par concours :</label>
    <select name="concours" id="concours">
        <option value="">-- Tous --</option>
        <?php foreach ($concours_list as $c): ?>
            <option value="<?= $c['id'] ?>" <?= ($filtre_concours == $c['id']) ? 'selected' : '' ?>>
                <?= htmlspecialchars($c['titre']) ?>
            </option>
        <?php endforeach; ?>
    </select>

    <label for="date">Filtrer par date :</label>
    <input type="date" name="date" id="date" value="<?= htmlspecialchars($filtre_date) ?>">

    <button type="submit">Rechercher</button>
    <a href="historique.php">Réinitialiser</a>
</form>

<br>

<?php if (count($corrections) > 0): ?>
    <table border="1" cellpadding="6">
        <thead>
            <tr>
                <th>Identifiant Anonyme</th>
                <th>Concours</th>
                <th>Note Totale</th>
                <th>Date de Correction</th>
                <th>Commentaire Global</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($corrections as $corr): ?>
                <tr>
                    <td><?= htmlspecialchars($corr['identifiant_anon']) ?></td>
                    <td><?= htmlspecialchars($corr['concours']) ?></td>
                    <td><?= (int)$corr['note_total'] ?></td>
                    <td><?= htmlspecialchars($corr['date_correction']) ?></td>
                    <td><?= nl2br(htmlspecialchars($corr['commentaire_global'])) ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
<?php else: ?>
    <p>Aucune correction trouvée avec ces critères.</p>
<?php endif; ?>

<p><a href="dashboard.php">← Retour au tableau de bord</a></p>
</body>
</html>
