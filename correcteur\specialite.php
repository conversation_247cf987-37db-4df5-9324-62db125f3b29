<?php
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/helpers.php';

verifier_connexion();
verifier_role('correcteur');

$id_correcteur = $_SESSION['user']['id'];
$message = '';
$type_message = '';

// Récupérer la spécialité actuelle du correcteur
$stmt = $pdo->prepare("SELECT specialite FROM specialite WHERE user_id = ?");
$stmt->execute([$id_correcteur]);
$specialite_actuelle = $stmt->fetchColumn();

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['specialite'])) {
        $specialite = trim($_POST['specialite']);
        
        try {
            $pdo->beginTransaction();

            if ($specialite_actuelle) {
                // Mettre à jour la spécialité existante
                $stmt = $pdo->prepare("UPDATE specialite SET specialite = ? WHERE user_id = ?");
                $stmt->execute([$specialite, $id_correcteur]);
            } else {
                // Insérer une nouvelle spécialité
                $stmt = $pdo->prepare("INSERT INTO specialite (user_id, specialite) VALUES (?, ?)");
                $stmt->execute([$id_correcteur, $specialite]);
            }

            $pdo->commit();
            $message = "Votre spécialité a été mise à jour avec succès.";
            $type_message = "success";
            $specialite_actuelle = $specialite;
        } catch (Exception $e) {
            $pdo->rollBack();
            $message = "Une erreur est survenue lors de la mise à jour de votre spécialité.";
            $type_message = "error";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gérer ma spécialité - Correcteur</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f6f9;
            margin: 0;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .message {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
            text-align: center;
        }

        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: #2c3e50;
            font-weight: bold;
        }

        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background-color: #2980b9;
        }

        .btn-secondary {
            background-color: #95a5a6;
        }

        .btn-secondary:hover {
            background-color: #7f8c8d;
        }

        .actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Gérer ma spécialité</h1>

        <?php if ($message): ?>
            <div class="message <?= $type_message ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="">
            <div class="form-group">
                <label for="specialite">Votre spécialité :</label>
                <input type="text" id="specialite" name="specialite" value="<?= htmlspecialchars($specialite_actuelle) ?>" required>
            </div>

            <div class="actions">
                <button type="submit" class="btn">Enregistrer ma spécialité</button>
                <a href="dashboard.php" class="btn btn-secondary">Retour au tableau de bord</a>
            </div>
        </form>
    </div>
</body>
</html> 