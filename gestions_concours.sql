CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(100) NOT NULL,
  `prenom` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `mot_de_passe` varchar(255) NOT NULL,
  `role` enum('admin','candidat','correcteur') NOT NULL,
  `actif` tinyint(1) DEFAULT 1,
  `date_inscription` timestamp NOT NULL DEFAULT current_timestamp(),
  `specialite` varchar(100) DEFAULT NULL,
  `est_correcteur` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


  CREATE TABLE `concours` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `titre` varchar(255) NOT NULL,
  `date_debut` date NOT NULL,
  `date_fin` date NOT NULL,
  `description` text DEFAULT NULL,
  `date_concours` date DEFAULT NULL,
  `resultats_publies` tinyint(1) DEFAULT 0,
  `date_publication` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



  CREATE TABLE `matiere` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `nom` VARCHAR(100) NOT NULL,
  `coefficient` FLOAT DEFAULT 1,
  `id_concours` INT(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id_concours` (`id_concours`),
  CONSTRAINT `matiere_ibfk_1` FOREIGN KEY (`id_concours`) REFERENCES `concours` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


CREATE TABLE `copie` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `id_candidat` INT(11) NOT NULL,
  `id_concours` INT(11) NOT NULL,
  `identifiant_anon` VARCHAR(100) NOT NULL,
  `fichier_path` TEXT NOT NULL,
  `date_depot` DATETIME DEFAULT current_timestamp(),
  `statut` ENUM('en_attente','corrigee') DEFAULT 'en_attente',
  `id_matiere` INT(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `identifiant_anon` (`identifiant_anon`),
  KEY `id_candidat` (`id_candidat`),
  KEY `id_concours` (`id_concours`),
  KEY `id_matiere` (`id_matiere`),
  CONSTRAINT `copie_ibfk_1` FOREIGN KEY (`id_candidat`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `copie_ibfk_2` FOREIGN KEY (`id_concours`) REFERENCES `concours` (`id`) ON DELETE CASCADE,
  CONSTRAINT `copie_ibfk_3` FOREIGN KEY (`id_matiere`) REFERENCES `matiere` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


  CREATE TABLE `attribution` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_correcteur` int(11) NOT NULL,
  `id_copie` int(11) NOT NULL,
  `id_matiere` int(11) DEFAULT NULL,
  `date_attribution` datetime DEFAULT current_timestamp(),
  `auto_assignee` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_correcteur` (`id_correcteur`,`id_copie`),
  KEY `id_copie` (`id_copie`),
  CONSTRAINT `attribution_ibfk_1` FOREIGN KEY (`id_correcteur`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `attribution_ibfk_2` FOREIGN KEY (`id_copie`) REFERENCES `copie` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


  CREATE TABLE `correction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_attribution` int(11) NOT NULL,
  `date_correction` datetime DEFAULT current_timestamp(),
  `commentaire_global` text DEFAULT NULL,
  `note_total` int(11) DEFAULT NULL,
  `id_matiere` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id_attribution` (`id_attribution`),
  KEY `id_matiere` (`id_matiere`),
  CONSTRAINT `correction_ibfk_1` FOREIGN KEY (`id_attribution`) REFERENCES `attribution` (`id`) ON DELETE CASCADE,
  CONSTRAINT `correction_ibfk_2` FOREIGN KEY (`id_matiere`) REFERENCES `matiere` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


CREATE TABLE `inscription_concours` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_candidat` int(11) NOT NULL,
  `id_concours` int(11) NOT NULL,
  `date_inscription` datetime DEFAULT current_timestamp(),
  `niveau_etudes` varchar(50) DEFAULT NULL,
  `etablissement` varchar(100) DEFAULT NULL,
  `motivation` text DEFAULT NULL,
  `accepte_conditions` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_inscription` (`id_candidat`,`id_concours`),
  KEY `id_concours` (`id_concours`),
  CONSTRAINT `inscription_concours_ibfk_1` FOREIGN KEY (`id_candidat`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `inscription_concours_ibfk_2` FOREIGN KEY (`id_concours`) REFERENCES `concours` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `grille_evaluation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_concours` int(11) NOT NULL,
  `critere` text NOT NULL,
  `bareme` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `id_concours` (`id_concours`),
  CONSTRAINT `grille_evaluation_ibfk_1` FOREIGN KEY (`id_concours`) REFERENCES `concours` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `note_detaillee` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_correction` int(11) NOT NULL,
  `id_critere` int(11) NOT NULL,
  `note` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `id_correction` (`id_correction`),
  KEY `id_critere` (`id_critere`),
  CONSTRAINT `note_detaillee_ibfk_1` FOREIGN KEY (`id_correction`) REFERENCES `correction` (`id`) ON DELETE CASCADE,
  CONSTRAINT `note_detaillee_ibfk_2` FOREIGN KEY (`id_critere`) REFERENCES `grille_evaluation` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;