<?php
session_start();
require_once '../includes/helpers.php';


function utilisateur() {
    return $_SESSION['user'] ?? null;
}function est_admin() {
    return a_le_role('admin');
}function est_correcteur() {
    return a_le_role('correcteur');
}
function est_candidat() {
    return a_le_role('candidat');
}
function verifier_connexion() {
    if (!est_connecte()) {
        header('Location: /Gestions_concours/public/login.php');
        exit;
    }
}
function verifier_role(string $role) {
    if (!a_le_role($role)) {
        http_response_code(403);
        echo "⛔ Accès interdit - Rôle requis : $role";
        exit;
    }
}
