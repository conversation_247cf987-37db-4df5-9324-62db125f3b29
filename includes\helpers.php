<?php
function e($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
function rediriger($url) {
    header("Location: $url");
    exit;
}
function est_connecte() {
    return isset($_SESSION['user']);
}
function utilisateur_connecte() {
    return $_SESSION['user'] ?? null;
}
function a_le_role($role) {
    return est_connecte() && $_SESSION['user']['role'] === $role;
}
function maintenant() {
    return date('Y-m-d H:i:s');
}
function formater_date($date) {
    return date('d/m/Y H:i', strtotime($date));
}
function email_valide($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}
