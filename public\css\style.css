body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f7f7f7;
    color: #333;
}

header, footer {
    background-color: #004466;
    color: white;
    padding: 15px;
    text-align: center;
}

h1, h2 {
    color: #004466;
}

a {
    color: #0066cc;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

form {
    margin-bottom: 20px;
    background: white;
    padding: 15px;
    border: 1px solid #ccc;
}

input, select, textarea, button {
    display: block;
    width: 100%;
    padding: 8px;
    margin: 5px 0 15px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

button {
    background-color: #004466;
    color: white;
    border: none;
    cursor: pointer;
}

button:hover {
    background-color: #006699;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background-color: white;
}

table, th, td {
    border: 1px solid #ccc;
}

th, td {
    padding: 10px;
    text-align: left;
}

@media screen and (max-width: 600px) {
    table, thead, tbody, th, td, tr {
        display: block;
    }

    th {
        background-color: #eee;
        font-weight: bold;
    }

    td {
        border: none;
        border-bottom: 1px solid #ccc;
        position: relative;
        padding-left: 50%;
    }

    td:before {
        position: absolute;
        left: 10px;
        top: 10px;
        white-space: nowrap;
        font-weight: bold;
    }
}
.layout {
    display: flex;
    min-height: 100vh;
}

/* Menu latéral */
.sidebar {
    width: 220px;
    background-color: #004466; 
    color: white;
    padding: 20px;
}

.sidebar h3 {
    color: #fff;
    margin-top: 0;
}

.sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar li {
    margin: 15px 0;
}

.sidebar a {
    color: #fff;
    text-decoration: none;
    display: block;
    padding: 8px;
    border-radius: 4px;
}

.sidebar a:hover {
    background-color: #006699;
}

/* Contenu principal */
.content {
    flex: 1;
    padding: 30px;
    background-color: #f7f7f7;
}
/* Styles spécifiques au tableau de bord admin */
body {
    background-color: #f0f4f8;
}

h2 {
    color: #004466;
    margin-bottom: 20px;
    font-size: 1.8em;
}

ul {
    list-style: none;
    padding: 0;
    max-width: 500px;
    margin: 20px auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
}

ul li {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    font-weight: bold;
}

ul li:last-child {
    border-bottom: none;
}

a {
    display: inline-block;
    margin: 10px;
    padding: 10px 15px;
    background-color: #004466;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

a:hover {
    background-color: #006699;
}

