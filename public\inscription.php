<?php
require_once '../config/database.php';
require_once '../includes/helpers.php';

$message = '';
$type_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nom = trim($_POST['nom'] ?? '');
    $prenom = trim($_POST['prenom'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $role = $_POST['role'] ?? '';
    $specialite = trim($_POST['specialite'] ?? '');

    if (empty($nom) || empty($prenom) || empty($email) || empty($password) || empty($role)) {
        $message = "Tous les champs sont obligatoires.";
        $type_message = "error";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = "L'adresse email n'est pas valide.";
        $type_message = "error";
    } elseif ($role === 'correcteur' && empty($specialite)) {
        $message = "La spécialité est obligatoire pour les correcteurs.";
        $type_message = "error";
    } else {
        // Vérifier si l'email existe déjà
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$email]);
        if ($stmt->fetch()) {
            $message = "Cette adresse email est déjà utilisée.";
            $type_message = "error";
        } else {
            try {
                $pdo->beginTransaction();

                // Insérer dans la table users avec la spécialité
                $stmt = $pdo->prepare("INSERT INTO users (nom, prenom, email, mot_de_passe, role, specialite, date_inscription) VALUES (?, ?, ?, ?, ?, ?, NOW())");
                $stmt->execute([$nom, $prenom, $email, password_hash($password, PASSWORD_DEFAULT), $role, $specialite]);

                $pdo->commit();
                $message = "Inscription réussie ! Vous pouvez maintenant vous connecter.";
                $type_message = "success";
            } catch (Exception $e) {
                $pdo->rollBack();
                $message = "Une erreur est survenue lors de l'inscription : " . $e->getMessage();
                $type_message = "error";
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Inscription</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Century', sans-serif;
        }

        body {
            background: url('../images/image.png') no-repeat center center fixed;
            background-size: cover;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        form {
            background-color: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 400px;
        }

        h2 {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: bold;
        }

        input, select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        input:focus, select:focus {
            border-color: #4CAF50;
            outline: none;
        }

        button {
            width: 100%;
            padding: 12px;
            background-color: #4CAF50;
            color: white;
            font-size: 18px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #45a049;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 5px;
            font-size: 15px;
            text-align: center;
        }

        .error {
            background-color: #ffebee;
            color: #c62828;
            border: 1px solid #ef9a9a;
        }

        .success {
            background-color: #e8f5e9;
            color: #2e7d32;
            border: 1px solid #a5d6a7;
        }

        p {
            text-align: center;
            margin-top: 15px;
        }

        p a {
            color: #2196F3;
            text-decoration: none;
            font-weight: bold;
        }

        p a:hover {
            text-decoration: underline;
        }

        .specialite-field {
            display: none;
        }

        @media (max-width: 500px) {
            form {
                padding: 20px;
            }

            h2 {
                font-size: 22px;
            }
        }
    </style>
</head>
<body>
    <form method="POST" onsubmit="return validateForm()">
        <h2><i class="fas fa-user-plus"></i> Inscription</h2>

        <?php if ($message): ?>
            <div class="message <?= $type_message ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <div class="form-group">
            <label for="nom">Nom</label>
            <input type="text" id="nom" name="nom" value="<?= htmlspecialchars($_POST['nom'] ?? '') ?>" required>
        </div>

        <div class="form-group">
            <label for="prenom">Prénom</label>
            <input type="text" id="prenom" name="prenom" value="<?= htmlspecialchars($_POST['prenom'] ?? '') ?>" required>
        </div>

        <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" name="email" value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" required>
        </div>

        <div class="form-group">
            <label for="password">Mot de passe</label>
            <input type="password" id="password" name="password" required minlength="6">
        </div>

        <div class="form-group">
            <label for="role">Rôle</label>
            <select id="role" name="role" required onchange="toggleSpecialite()">
                <option value="">Choisissez un rôle</option>
                <option value="candidat" <?= (isset($_POST['role']) && $_POST['role'] === 'candidat') ? 'selected' : '' ?>>Candidat</option>
                <option value="correcteur" <?= (isset($_POST['role']) && $_POST['role'] === 'correcteur') ? 'selected' : '' ?>>Correcteur</option>
            </select>
        </div>

        <div class="form-group specialite-field" id="specialiteField">
            <label for="specialite">Spécialité :</label>
            <input type="text" id="specialite" name="specialite" placeholder="Entrez votre spécialité (ex: Mathématiques, Français, etc.)">
        </div>

        <button type="submit">S'inscrire</button>

        <p><a href="login.php"><i class="fas fa-sign-in-alt"></i> Déjà inscrit ? Connexion</a></p>
    </form>

    <script>
        function toggleSpecialite() {
            const role = document.getElementById('role').value;
            const specialiteField = document.getElementById('specialiteField');
            const specialiteInput = document.getElementById('specialite');
            
            if (role === 'correcteur') {
                specialiteField.style.display = 'block';
                specialiteInput.required = true;
            } else {
                specialiteField.style.display = 'none';
                specialiteInput.required = false;
            }
        }

        function validateForm() {
            const role = document.getElementById('role').value;
            const specialite = document.getElementById('specialite').value;
            
            if (role === 'correcteur' && !specialite.trim()) {
                alert('Veuillez spécifier votre spécialité.');
                return false;
            }
            return true;
        }

        // Initialiser l'affichage du champ spécialité au chargement
        document.addEventListener('DOMContentLoaded', toggleSpecialite);
    </script>
</body>
</html>
